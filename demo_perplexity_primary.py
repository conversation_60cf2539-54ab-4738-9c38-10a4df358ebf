#!/usr/bin/env python3
"""
演示脚本：展示 Perplexity AI 作为 Agent Laboratory 主要文献综述引擎的功能
"""

import os
import sys
from typing import Dict, Any

def demo_perplexity_as_primary():
    """演示 Perplexity AI 作为主要文献综述引擎的功能"""
    
    print("🚀 Agent Laboratory - Perplexity AI 主要文献综述引擎演示")
    print("=" * 70)
    
    # 检查 API Key
    api_key = os.getenv('PERPLEXITY_API_KEY')
    if not api_key:
        print("❌ 请设置 PERPLEXITY_API_KEY 环境变量")
        print("export PERPLEXITY_API_KEY='your_api_key_here'")
        return False
    
    try:
        # 导入必要的模块
        from perplexity_search import PerplexitySearch
        from perplexity_config import setup_perplexity_for_agent_lab, PerplexityConfig
        
        print("✅ 模块导入成功")
        
        # 演示研究主题
        research_topics = [
            "机器学习在材料科学中的应用",
            "深度学习用于药物发现",
            "人工智能在医疗诊断中的应用"
        ]
        
        for i, topic in enumerate(research_topics, 1):
            print(f"\n📚 演示 {i}: {topic}")
            print("-" * 50)
            
            # 设置优化配置
            config = setup_perplexity_for_agent_lab(topic, budget=5.0)
            
            if "error" in config:
                print(f"❌ 配置错误: {config['error']}")
                continue
            
            print(f"🎯 推荐策略: {config['strategy']['primary_approach']}")
            print(f"🤖 使用模型: {config['strategy']['model']}")
            print(f"💰 预估成本: ${config['strategy']['estimated_cost']:.3f}")
            print(f"🔍 搜索域数量: {len(config['domain_filters'])}")
            
            # 初始化 Perplexity 搜索引擎
            perplexity = PerplexitySearch(api_key)
            
            # 演示快速文献搜索
            print(f"\n🔍 正在搜索相关文献...")
            try:
                search_results = perplexity.search_literature(topic, num_papers=3)
                if search_results and not search_results.startswith("Error"):
                    print("✅ 文献搜索成功!")
                    print(f"📄 结果预览 (前200字符):")
                    print(search_results[:200] + "...")
                else:
                    print(f"❌ 搜索失败: {search_results}")
            except Exception as e:
                print(f"❌ 搜索异常: {e}")
            
            # 演示综合文献分析（如果可用）
            if hasattr(perplexity, 'comprehensive_literature_analysis'):
                print(f"\n📊 正在进行综合文献分析...")
                try:
                    analysis = perplexity.comprehensive_literature_analysis(topic)
                    if analysis and "error" not in analysis:
                        print("✅ 综合分析成功!")
                        print(f"📈 分析组件: {list(analysis.keys())}")
                        
                        # 格式化综述
                        formatted_review = perplexity.format_comprehensive_review(analysis, topic)
                        print(f"📝 格式化综述长度: {len(formatted_review)} 字符")
                    else:
                        print(f"❌ 分析失败: {analysis.get('error', '未知错误')}")
                except Exception as e:
                    print(f"❌ 分析异常: {e}")
            
            print(f"\n{'='*50}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有必要的依赖")
        return False
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        return False

def demo_agent_lab_integration():
    """演示与 Agent Laboratory 的集成"""
    
    print("\n🔧 Agent Laboratory 集成演示")
    print("=" * 50)
    
    print("📋 在 Agent Laboratory 中使用 Perplexity AI:")
    print()
    print("1. 🎯 PhD 学生代理会自动推荐使用 PERPLEXITY_REVIEW 命令")
    print("2. 🚀 系统优先使用 Perplexity 进行文献综述")
    print("3. 💡 ArXiv 搜索作为补充工具")
    print("4. 📊 自动成本控制和预算管理")
    print("5. 🎨 智能模板选择和格式化")
    
    print("\n📝 推荐的工作流程:")
    print("   Step 1: 使用 PERPLEXITY_REVIEW 获得综合文献综述")
    print("   Step 2: 使用 SUMMARY 搜索特定 ArXiv 论文（如需要）")
    print("   Step 3: 使用 ADD_PAPER 添加重要论文到综述")
    print("   Step 4: 系统自动完成文献综述阶段")
    
    print("\n🎯 优势对比:")
    print("   传统方式: ArXiv 搜索 → 手动分析 → 基础综述")
    print("   Perplexity: 多数据库 → AI 分析 → 专业综述")

def show_usage_examples():
    """显示使用示例"""
    
    print("\n📖 使用示例")
    print("=" * 40)
    
    print("🔧 环境设置:")
    print("export PERPLEXITY_API_KEY='your_perplexity_api_key'")
    print("export GOOGLE_API_KEY='your_google_api_key'")
    print()
    
    print("🚀 启动 Agent Laboratory (推荐配置):")
    print("python ai_lab_repo.py \\")
    print("  --research-topic '机器学习在材料科学中的应用' \\")
    print("  --api-key $GOOGLE_API_KEY \\")
    print("  --perplexity-api-key $PERPLEXITY_API_KEY \\")
    print("  --literature-engine both \\")
    print("  --language 中文")
    print()
    
    print("🧪 测试 Perplexity 集成:")
    print("python test_perplexity.py")
    print()
    
    print("📚 查看详细文档:")
    print("cat PERPLEXITY_USAGE_GUIDE.md")

def main():
    """主函数"""
    
    print("🎉 欢迎使用 Agent Laboratory - Perplexity AI 主要文献综述引擎!")
    print()
    
    # 运行演示
    demo_success = demo_perplexity_as_primary()
    
    # 显示集成信息
    demo_agent_lab_integration()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 70)
    if demo_success:
        print("✅ 演示完成! Perplexity AI 已准备好作为主要文献综述引擎使用")
        print("🚀 现在可以启动 Agent Laboratory 体验增强的文献综述功能!")
    else:
        print("⚠️  演示过程中遇到一些问题，请检查配置后重试")
        print("💡 确保设置了 PERPLEXITY_API_KEY 环境变量")
    
    print("\n📞 如需帮助，请查看:")
    print("   - PERPLEXITY_USAGE_GUIDE.md")
    print("   - PERPLEXITY_INTEGRATION.md")
    print("   - 运行 python test_perplexity.py 进行完整测试")

if __name__ == "__main__":
    main()
