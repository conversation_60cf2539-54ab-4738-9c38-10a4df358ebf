import subprocess
import os
import glob
import re
from datetime import datetime
from papersolver import PaperSolver # 假设 papersolver.py 在同一目录下或PYTHONPATH中
# from utils import get_config_values # 假设有这个工具函数获取配置

# 获取 agentlaboratory 的根目录，以便正确处理相对路径
# 这个脚本 (experiment_to_paper_agent.py) 应该在 agentlaboratory 目录下
AGENT_LAB_BASE_DIR = os.path.dirname(os.path.abspath(__file__))

class ExperimentToPaperAgent:
    def __init__(self, config=None):
        # 优先使用传入的config，否则尝试从默认路径加载
        if config:
            self.config = config
        else:
            # self.config = get_config_values() # 使用您项目中的配置加载方式
            print("Warning: No config provided and get_config_values is commented out. Using an empty config dict.")
            self.config = {} # Fallback to an empty config if no config is passed and get_config_values is disabled

        self.experiment_script_path = os.path.join(AGENT_LAB_BASE_DIR, "silicon_yield_predictor_english.py")
        self.experiment_output_folder_abs = None
        self.experiment_output_folder_relative = None # 相对于 AGENT_LAB_BASE_DIR
        self.experiment_results_summary = "Machine learning experiments were conducted, yielding predictive models."
        self.experiment_insights_summary = "Model interpretation using SHAP and PDP provided insights into key features."
        self.generated_image_paths_relative = [] # 相对于 AGENT_LAB_BASE_DIR
        self.experiment_code_content = ""

    def _run_experiment(self):
        print(f"Stage 1: Running experiment script: {self.experiment_script_path}...")
        
        # 确保执行目录是脚本所在的目录，这样脚本内部的相对路径（如 data/Si2025_5_4.csv）才能正确解析
        script_dir = os.path.dirname(self.experiment_script_path)

        try:
            process = subprocess.run(
                ['python', os.path.basename(self.experiment_script_path)], # 只传递脚本名，因为cwd会设置
                capture_output=True, text=True, check=True,
                cwd=script_dir # 在脚本所在目录执行
            )
            stdout = process.stdout
            stderr = process.stderr # 捕获标准错误以供调试
            print("--- Experiment Script STDOUT ---")
            print(stdout)
            if stderr:
                print("--- Experiment Script STDERR ---")
                print(stderr)

            # 从stdout解析输出文件夹名称
            # predictor 脚本会打印 "All plots and results saved to: <folder_name>"
            # 或者 "Created output folder: <folder_name>"
            output_folder_name = None
            for line in stdout.splitlines():
                if "All plots and results saved to:" in line:
                    output_folder_name = line.split("All plots and results saved to:")[1].strip()
                    break
                elif "Created output folder:" in line: # 备用匹配
                    output_folder_name = line.split("Created output folder:")[1].strip()
                    break
            
            if not output_folder_name:
                print("Warning: Could not determine experiment output folder name from script stdout.")
                # 尝试从可能的默认位置查找最新的文件夹 (如果脚本没有正确打印)
                # 这是一种后备方案，依赖于 predictor 脚本的行为
                plot_folders = glob.glob(os.path.join(script_dir, "silicon_analysis_plots_*"))
                if plot_folders:
                    output_folder_name = os.path.basename(max(plot_folders, key=os.path.getmtime))
                    print(f"Found latest plots folder as fallback: {output_folder_name}")

            if not output_folder_name:
                raise Exception("Failed to determine experiment output folder.")

            self.experiment_output_folder_relative = output_folder_name # 这是相对于 script_dir 的
            self.experiment_output_folder_abs = os.path.join(script_dir, output_folder_name)
            print(f"Determined experiment output folder (relative to script_dir): {self.experiment_output_folder_relative}")
            print(f"Absolute path to output folder: {self.experiment_output_folder_abs}")

            # 收集图片路径 (相对于 AGENT_LAB_BASE_DIR)
            if os.path.exists(self.experiment_output_folder_abs):
                image_files = [f for f in os.listdir(self.experiment_output_folder_abs) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif'))]
                # PaperSolver 需要的路径是相对于其工作目录（通常是 AGENT_LAB_BASE_DIR）的
                # predictor 生成的 output_folder_name 已经是相对于 AGENT_LAB_BASE_DIR 的子目录名
                self.generated_image_paths_relative = [
                    os.path.join(self.experiment_output_folder_relative, img_file) for img_file in image_files
                ]
                print(f"Collected {len(self.generated_image_paths_relative)} image paths (relative to AGENT_LAB_BASE_DIR):")
                for p in self.generated_image_paths_relative[:5]: print(f"  - {p}") # 打印前5个
            else:
                print(f"Warning: Experiment output folder '{self.experiment_output_folder_abs}' not found after script execution.")

            # 提取结果摘要和洞察 (简化版，从stdout中提取R2等)
            r2_test_xgb_match = re.search(r"Test R-squared: (\d\.\d+)", stdout) # 匹配XGBoost的
            if r2_test_xgb_match:
                self.experiment_results_summary = f"XGBoost model achieved Test R-squared: {r2_test_xgb_match.group(1)}. "
            
            stacking_r2_match = re.search(r"Stacking Model Evaluation Results.*?Test R²: (\d\.\d+)", stdout, re.DOTALL)
            if stacking_r2_match:
                 self.experiment_results_summary += f"Stacking ensemble model Test R-squared: {stacking_r2_match.group(1)}. "
            
            if not self.experiment_results_summary.startswith("XGBoost") and not self.experiment_results_summary.startswith("Stacking"):
                 self.experiment_results_summary = "Machine learning experiments completed. Key metrics extracted."


            # 洞察可以更通用些，因为具体SHAP数值难提取
            self.experiment_insights_summary = "SHAP analysis was performed to identify key feature importances and dependencies. PDP plots illustrated marginal effects of top features. These analyses aid in understanding model predictions and underlying data relationships."

            print(f"Extracted results summary: {self.experiment_results_summary}")
            print(f"Extracted insights summary: {self.experiment_insights_summary}")

        except subprocess.CalledProcessError as e:
            print(f"Error running experiment script: {e}")
            print(f"Stdout: {e.stdout}")
            print(f"Stderr: {e.stderr}")
            raise
        except Exception as e:
            print(f"An unexpected error occurred during experiment execution: {e}")
            raise

        # 读取实验代码内容
        try:
            with open(self.experiment_script_path, 'r', encoding='utf-8') as f:
                self.experiment_code_content = f.read()
        except Exception as e:
            print(f"Error reading experiment script content: {e}")
            self.experiment_code_content = f"# Failed to read script content from {self.experiment_script_path}\n"


    def _prepare_papersolver_inputs(self):
        print("Stage 2: Preparing inputs for PaperSolver...")
        # 这些值部分来自config，部分来自_run_experiment的产出
        
        # 从 config 中获取，如果不存在则使用默认值
        # utils.get_config_values() 返回的是一个字典
        cfg = self.config if isinstance(self.config, dict) else {}

        return {
            "llm_str": cfg.get("llm_backend", "openai-compatible"), # 对应原ai_lab_repo1.py中的llm_backend
            "notes": cfg.get("papersolver_notes", "The report should be comprehensive, detailing the methodology, results, and their implications. Ensure all provided figures are appropriately referenced and discussed."),
            "insights": self.experiment_insights_summary,
            "plan": cfg.get("research_topic", f"Automated research report on silicon yield prediction using data from {cfg.get('data_file', 'Si2025_5_4.csv')}."), # 使用 research_topic 作为 plan 的基础
            "exp_code": self.experiment_code_content,
            "exp_results": self.experiment_results_summary,
            "lit_review": cfg.get("literature_review_content", ""), # 通常由文献综述阶段提供
            "ref_papers": cfg.get("reference_papers_list", []),
            "topic": cfg.get("research_topic", "Silicon Yield Prediction with Advanced Machine Learning"),
            "openai_api_key": cfg.get("api_key"), # 对应原api_key
            "openai_base_url": cfg.get("openai_base_url"),
            "openai_model_name": cfg.get("openai_model_name"),
            "compile_pdf": False, # PaperSolver 内部逻辑是生成Markdown
            "author_name": cfg.get("author_name", "AI Research Collaboratory"),
            "generated_images": self.generated_image_paths_relative # 使用相对路径
        }

    def generate_report(self):
        # PaperSolver 会在它被调用的当前工作目录创建 research_paper.md
        # 我们希望这个CWD是 AGENT_LAB_BASE_DIR
        original_cwd = os.getcwd()
        os.chdir(AGENT_LAB_BASE_DIR)
        print(f"Changed CWD to: {os.getcwd()} for PaperSolver execution.")

        try:
            self._run_experiment() # 产出图片和摘要
            papersolver_inputs = self._prepare_papersolver_inputs()

            print("Stage 3: Invoking PaperSolver...")
            solver = PaperSolver(**papersolver_inputs)
            solver.initial_solve() # 生成初始报告 "research_paper.md"

            # initial_solve 会在当前CWD (即AGENT_LAB_BASE_DIR) 创建 research_paper.md
            temp_report_path = os.path.join(AGENT_LAB_BASE_DIR, "research_paper.md") # 临时报告路径

            # 将生成的报告移动到 research_dir 并重命名
            research_dir = os.path.join(AGENT_LAB_BASE_DIR, "research_dir")
            if not os.path.exists(research_dir):
                os.makedirs(research_dir)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            final_report_name_in_research_dir = f"automated_ml_report_{timestamp}.md"
            destination_path = os.path.join(research_dir, final_report_name_in_research_dir)

            if os.path.exists(temp_report_path):
                os.rename(temp_report_path, destination_path)
                print(f"Report generation complete. Final report saved to: {destination_path}")
                return destination_path
            else:
                print(f"Error: PaperSolver did not produce '{temp_report_path}'.")
                return None
        except Exception as e:
            print(f"An error occurred in generate_report: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            os.chdir(original_cwd) # 恢复原始工作目录
            print(f"Restored CWD to: {os.getcwd()}")


if __name__ == "__main__":
    print(f"Running ExperimentToPaperAgent from: {os.getcwd()}")
    print(f"AGENT_LAB_BASE_DIR set to: {AGENT_LAB_BASE_DIR}")
    
    # 示例配置，实际应从 ai_lab_repo1.py 的参数或配置文件读取
    # 这里模拟一个简单的配置字典
    mock_config = {
        "llm_backend": "openai-compatible", # 或者您的LLM标识符
        "research_topic": "Automated Analysis of Silicon Yield using Machine Learning",
        "author_name": "ExperimentToPaper Agent",
        "data_file": "data/Si2025_5_4.csv", # 确保这个路径相对于 silicon_yield_predictor_english.py 是正确的
        "api_key": os.environ.get("OPENAI_API_KEY"), # 从环境变量读取
        "openai_base_url": os.environ.get("OPENAI_BASE_URL"), # 从环境变量读取
        "openai_model_name": os.environ.get("OPENAI_MODEL_NAME", "gpt-3.5-turbo"), # 从环境变量读取或默认
        "papersolver_notes": "Generate a detailed report based on the automated ML experimentation.",
        # "literature_review_content": "...", # 通常由其他Agent或阶段提供
        # "reference_papers_list": ["arXiv:xxxx.xxxxx"], # 通常由其他Agent或阶段提供
    }
    
    # 确保API密钥存在
    if not mock_config["api_key"]:
        print("Error: OPENAI_API_KEY environment variable not set. Please set it to run the example.")
    else:
        agent = ExperimentToPaperAgent(config=mock_config)
        report_file = agent.generate_report()
        if report_file:
            print(f"Successfully generated report: {report_file}")
        else:
            print("Failed to generate report.")
