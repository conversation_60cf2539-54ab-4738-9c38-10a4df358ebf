#!/usr/bin/env python3
"""
高级可视化工具 - 专门用于物理信息神经网络和理论计算的可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Polygon
from mpl_toolkits.mplot3d import Axes3D
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Tuple, Any, Optional
import os

# 导入配色方案
from .visualization_utils import CONTINUOUS_CMAP, CATEGORICAL_PALETTE

class PhaseDigramVisualizer:
    """相图可视化器"""
    
    def __init__(self):
        self.fig_size = (12, 10)
        self.dpi = 300
    
    def plot_ternary_phase_diagram(self, data: pd.DataFrame, output_path: str):
        """绘制三元相图"""
        # 归一化组成
        sio2 = data['SiO2'].values
        al2o3 = data['Al2O3'].values
        cao = 100 - sio2 - al2o3  # 假设剩余为CaO
        
        # 归一化到100%
        total = sio2 + al2o3 + cao
        sio2_norm = sio2 / total
        al2o3_norm = al2o3 / total
        cao_norm = cao / total
        
        # 转换为三角坐标
        x = 0.5 * (2 * cao_norm + al2o3_norm)
        y = (np.sqrt(3) / 2) * al2o3_norm
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.fig_size)
        
        # 绘制三角形边界
        triangle = Polygon([(0, 0), (1, 0), (0.5, np.sqrt(3)/2)], 
                          fill=False, edgecolor='black', linewidth=2)
        ax.add_patch(triangle)
        
        # 绘制数据点
        effective_si = data['Effective_Silicon'].values
        scatter = ax.scatter(x, y, c=effective_si, cmap=CONTINUOUS_CMAP, 
                           s=60, alpha=0.7, edgecolors='black', linewidth=0.5)
        
        # 添加标签
        ax.text(0, -0.05, 'CaO', ha='center', va='top', fontsize=14, fontweight='bold')
        ax.text(1, -0.05, 'SiO₂', ha='center', va='top', fontsize=14, fontweight='bold')
        ax.text(0.5, np.sqrt(3)/2 + 0.05, 'Al₂O₃', ha='center', va='bottom', 
               fontsize=14, fontweight='bold')
        
        # 添加网格线
        self._add_ternary_grid(ax)
        
        # 颜色条
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
        cbar.set_label('Effective Silicon Content (%)', fontsize=12)
        
        ax.set_xlim(-0.1, 1.1)
        ax.set_ylim(-0.1, np.sqrt(3)/2 + 0.1)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title('SiO₂-Al₂O₃-CaO Ternary Phase Diagram', fontsize=16, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()
    
    def _add_ternary_grid(self, ax):
        """添加三角网格"""
        # 添加网格线 (简化版本)
        for i in range(1, 10):
            frac = i / 10
            # 平行于底边的线
            x1, y1 = 0.5 * frac, (np.sqrt(3) / 2) * frac
            x2, y2 = 1 - 0.5 * frac, (np.sqrt(3) / 2) * frac
            ax.plot([x1, x2], [y1, y2], 'k-', alpha=0.3, linewidth=0.5)
            
            # 平行于左边的线
            x1, y1 = 0.5 * frac, (np.sqrt(3) / 2) * (1 - frac)
            x2, y2 = frac, 0
            ax.plot([x1, x2], [y1, y2], 'k-', alpha=0.3, linewidth=0.5)
            
            # 平行于右边的线
            x1, y1 = 0.5 + 0.5 * frac, (np.sqrt(3) / 2) * (1 - frac)
            x2, y2 = 1 - frac, 0
            ax.plot([x1, x2], [y1, y2], 'k-', alpha=0.3, linewidth=0.5)


class ThermodynamicVisualizer:
    """热力学可视化器"""
    
    def __init__(self):
        self.fig_size = (15, 10)
        self.dpi = 300
    
    def plot_ellingham_diagram(self, reactions: List[Dict], output_path: str):
        """绘制Ellingham图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        
        for i, reaction in enumerate(reactions):
            temps = np.array(reaction['temperatures'])
            gibbs = np.array(reaction['gibbs_energies'])
            
            color = CATEGORICAL_PALETTE[i % len(CATEGORICAL_PALETTE)]
            ax.plot(temps, gibbs, label=reaction['name'], 
                   linewidth=3, color=color, marker='o', markersize=4)
        
        # 添加平衡线
        ax.axhline(y=0, color='red', linestyle='--', linewidth=2, 
                  label='Equilibrium (ΔG = 0)')
        
        ax.set_xlabel('Temperature (°C)', fontsize=14)
        ax.set_ylabel('Gibbs Energy Change (kJ/mol)', fontsize=14)
        ax.set_title('Ellingham Diagram for Silicon Extraction Reactions', 
                    fontsize=16, fontweight='bold')
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加稳定性区域标注
        ax.fill_between(temps, -1000, 0, alpha=0.2, color='green', 
                       label='Thermodynamically Favorable')
        ax.fill_between(temps, 0, 1000, alpha=0.2, color='red', 
                       label='Thermodynamically Unfavorable')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()
    
    def plot_reaction_kinetics(self, temperature_data: np.ndarray, 
                             kinetic_data: Dict, output_path: str):
        """绘制反应动力学图"""
        fig, axes = plt.subplots(2, 2, figsize=self.fig_size)
        
        # 1. Arrhenius图
        ax1 = axes[0, 0]
        inv_temp = 1000 / (temperature_data + 273.15)  # 1000/T
        rate_constants = kinetic_data.get('rate_constants', np.exp(-150000 / (8.314 * (temperature_data + 273.15))))
        
        ax1.semilogy(inv_temp, rate_constants, 'o-', color=CATEGORICAL_PALETTE[0], 
                    linewidth=2, markersize=6)
        ax1.set_xlabel('1000/T (K⁻¹)')
        ax1.set_ylabel('Rate Constant (s⁻¹)')
        ax1.set_title('Arrhenius Plot')
        ax1.grid(True, alpha=0.3)
        
        # 2. 反应程度随时间变化
        ax2 = axes[0, 1]
        time_points = np.linspace(0, 120, 100)  # 0-120分钟
        for i, temp in enumerate([800, 900, 1000, 1100]):
            k = np.exp(-150000 / (8.314 * (temp + 273.15)))
            extent = 1 - np.exp(-k * time_points)
            color = plt.cm.viridis(i / 3)
            ax2.plot(time_points, extent, label=f'{temp}°C', 
                    color=color, linewidth=2)
        
        ax2.set_xlabel('Time (minutes)')
        ax2.set_ylabel('Reaction Extent')
        ax2.set_title('Reaction Progress vs Time')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 活化能分析
        ax3 = axes[1, 0]
        activation_energies = [100, 150, 200, 250]  # kJ/mol
        colors = plt.cm.viridis(np.linspace(0, 1, len(activation_energies)))
        
        for i, ea in enumerate(activation_energies):
            k_values = 1e10 * np.exp(-ea * 1000 / (8.314 * (temperature_data + 273.15)))
            ax3.semilogy(temperature_data, k_values, label=f'Ea = {ea} kJ/mol', 
                        color=colors[i], linewidth=2)
        
        ax3.set_xlabel('Temperature (°C)')
        ax3.set_ylabel('Rate Constant (s⁻¹)')
        ax3.set_title('Effect of Activation Energy')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 温度敏感性分析
        ax4 = axes[1, 1]
        temp_sensitivity = np.gradient(np.log(rate_constants), temperature_data)
        ax4.plot(temperature_data, temp_sensitivity, 'o-', 
                color=CATEGORICAL_PALETTE[2], linewidth=2, markersize=6)
        ax4.set_xlabel('Temperature (°C)')
        ax4.set_ylabel('d(ln k)/dT (°C⁻¹)')
        ax4.set_title('Temperature Sensitivity')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()


class PINNVisualizer:
    """物理信息神经网络可视化器"""
    
    def __init__(self):
        self.fig_size = (15, 10)
        self.dpi = 300
    
    def plot_physics_loss_components(self, training_history: Dict, output_path: str):
        """绘制物理损失组件"""
        fig, axes = plt.subplots(2, 2, figsize=self.fig_size)
        
        epochs = range(len(training_history['train_losses']))
        
        # 1. 总损失对比
        ax1 = axes[0, 0]
        ax1.plot(epochs, training_history['train_losses'], 
                label='Data Loss', color=CATEGORICAL_PALETTE[0], linewidth=2)
        ax1.plot(epochs, training_history['physics_losses'], 
                label='Physics Loss', color=CATEGORICAL_PALETTE[1], linewidth=2)
        ax1.plot(epochs, training_history['val_losses'], 
                label='Validation Loss', color=CATEGORICAL_PALETTE[2], linewidth=2)
        
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('Loss Components During Training')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 2. 损失比例
        ax2 = axes[0, 1]
        physics_ratio = np.array(training_history['physics_losses']) / np.array(training_history['train_losses'])
        ax2.plot(epochs, physics_ratio, color=CATEGORICAL_PALETTE[3], linewidth=2)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Physics Loss / Data Loss')
        ax2.set_title('Physics Constraint Influence')
        ax2.grid(True, alpha=0.3)
        
        # 3. 学习率调度
        ax3 = axes[1, 0]
        # 模拟学习率变化
        lr_schedule = 0.001 * (0.5 ** (np.array(epochs) // 100))
        ax3.plot(epochs, lr_schedule, color=CATEGORICAL_PALETTE[4], linewidth=2)
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_title('Learning Rate Schedule')
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')
        
        # 4. 收敛分析
        ax4 = axes[1, 1]
        # 计算损失的移动平均
        window = 10
        if len(training_history['val_losses']) > window:
            val_smooth = np.convolve(training_history['val_losses'], 
                                   np.ones(window)/window, mode='valid')
            epochs_smooth = epochs[window-1:]
            ax4.plot(epochs_smooth, val_smooth, color=CATEGORICAL_PALETTE[5], linewidth=2)
        
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Smoothed Validation Loss')
        ax4.set_title('Convergence Analysis')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()
    
    def plot_constraint_violations(self, violations_data: Dict, output_path: str):
        """绘制约束违反分析"""
        fig, axes = plt.subplots(2, 2, figsize=self.fig_size)
        
        # 1. 质量守恒违反
        ax1 = axes[0, 0]
        mass_violations = violations_data.get('mass_conservation', [])
        ax1.hist(mass_violations, bins=30, alpha=0.7, color=CATEGORICAL_PALETTE[0])
        ax1.set_xlabel('Mass Conservation Violation')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Mass Conservation Constraint')
        ax1.grid(True, alpha=0.3)
        
        # 2. 热力学约束违反
        ax2 = axes[0, 1]
        thermo_violations = violations_data.get('thermodynamic', [])
        ax2.hist(thermo_violations, bins=30, alpha=0.7, color=CATEGORICAL_PALETTE[1])
        ax2.set_xlabel('Thermodynamic Constraint Violation')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Thermodynamic Constraint')
        ax2.grid(True, alpha=0.3)
        
        # 3. 动力学约束违反
        ax3 = axes[1, 0]
        kinetic_violations = violations_data.get('kinetic', [])
        ax3.hist(kinetic_violations, bins=30, alpha=0.7, color=CATEGORICAL_PALETTE[2])
        ax3.set_xlabel('Kinetic Constraint Violation')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Kinetic Constraint')
        ax3.grid(True, alpha=0.3)
        
        # 4. 总体约束满足度
        ax4 = axes[1, 1]
        constraint_types = ['Mass', 'Thermodynamic', 'Kinetic', 'Phase Equilibrium']
        satisfaction_rates = [
            (np.array(mass_violations) < 0.1).mean() * 100,
            (np.array(thermo_violations) < 0.1).mean() * 100,
            (np.array(kinetic_violations) < 0.1).mean() * 100,
            85.0  # 模拟相平衡满足率
        ]
        
        colors = plt.cm.viridis(np.linspace(0.2, 0.8, len(constraint_types)))
        bars = ax4.bar(constraint_types, satisfaction_rates, color=colors, alpha=0.8)
        ax4.set_ylabel('Satisfaction Rate (%)')
        ax4.set_title('Physics Constraint Satisfaction')
        ax4.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, rate in zip(bars, satisfaction_rates):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 1, 
                    f'{rate:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()


def create_interactive_3d_plot(data: pd.DataFrame, output_path: str):
    """创建交互式3D图表"""
    fig = go.Figure(data=go.Scatter3d(
        x=data['SiO2'],
        y=data['Al2O3'],
        z=data['Temp'],
        mode='markers',
        marker=dict(
            size=8,
            color=data['Effective_Silicon'],
            colorscale='Viridis',
            showscale=True,
            colorbar=dict(title="Effective Silicon (%)")
        ),
        text=[f'Si: {si:.1f}%<br>Temp: {temp}°C<br>Time: {time}min' 
              for si, temp, time in zip(data['Effective_Silicon'], data['Temp'], data['Time'])],
        hovertemplate='<b>%{text}</b><br>SiO₂: %{x:.1f}%<br>Al₂O₃: %{y:.1f}%<extra></extra>'
    ))
    
    fig.update_layout(
        title='3D Process Parameter Space',
        scene=dict(
            xaxis_title='SiO₂ Content (%)',
            yaxis_title='Al₂O₃ Content (%)',
            zaxis_title='Temperature (°C)'
        ),
        width=800,
        height=600
    )
    
    fig.write_html(output_path)


# 使用示例
if __name__ == "__main__":
    print("高级可视化工具模块已加载")
    print("包含功能：")
    print("- 三元相图可视化")
    print("- Ellingham图绘制")
    print("- 反应动力学分析")
    print("- PINN物理约束可视化")
    print("- 交互式3D图表")
