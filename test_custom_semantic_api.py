#!/usr/bin/env python3
"""
测试自定义Semantic Scholar API配置
"""

import os
from enhanced_semantic_search import SemanticScholarSearch

def test_custom_api():
    """测试自定义API配置"""
    
    # 设置测试API密钥（请替换为您的实际密钥）
    test_api_key = "sk-xxxxxxxxx"  # 请替换为您的实际API密钥
    
    print("🔍 测试自定义Semantic Scholar API...")
    
    # 初始化搜索引擎
    search = SemanticScholarSearch(api_key=test_api_key, use_custom_api=True)
    
    # 测试搜索
    query = "machine learning SHAP"
    print(f"搜索查询: {query}")
    
    try:
        results = search.search_papers(query, limit=3)
        
        if results.get('data'):
            print(f"✅ 搜索成功！找到 {len(results['data'])} 篇论文")
            
            # 显示第一篇论文信息
            first_paper = results['data'][0]
            print(f"\n第一篇论文:")
            print(f"  标题: {first_paper.get('title', 'Unknown')}")
            print(f"  年份: {first_paper.get('year', 'Unknown')}")
            print(f"  引用数: {first_paper.get('citationCount', 0)}")
            
            # 测试生成文献综述
            print(f"\n📚 生成文献综述...")
            review_text, references = search.generate_literature_review(query, num_papers=3)
            
            print(f"✅ 文献综述生成成功")
            print(f"  综述长度: {len(review_text)} 字符")
            print(f"  参考文献数量: {len(references)}")
            
            # 保存结果
            search.save_search_results(query, results)
            
            return True
            
        else:
            print("❌ 搜索返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        return False

def test_environment_setup():
    """测试环境变量设置"""
    
    print("🔧 测试环境变量设置...")
    
    # 检查是否设置了API密钥
    api_key = os.getenv('SEMANTIC_SCHOLAR_API_KEY')
    if api_key:
        print(f"✅ 找到环境变量 SEMANTIC_SCHOLAR_API_KEY: {api_key[:10]}...")
        
        # 使用环境变量初始化
        search = SemanticScholarSearch()
        return True
    else:
        print("⚠️  未找到环境变量 SEMANTIC_SCHOLAR_API_KEY")
        print("请设置环境变量:")
        print("export SEMANTIC_SCHOLAR_API_KEY='sk-xxxxxxxxx'")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("测试自定义Semantic Scholar API配置")
    print("=" * 60)
    
    # 首先测试环境变量
    env_ok = test_environment_setup()
    
    print("\n" + "=" * 60)
    print("测试API功能")
    print("=" * 60)
    
    if env_ok:
        # 如果环境变量设置了，直接测试
        test_custom_api()
    else:
        print("请先设置API密钥环境变量，然后重新运行测试")
        print("\n使用方法:")
        print("1. 设置环境变量:")
        print("   export SEMANTIC_SCHOLAR_API_KEY='your_api_key_here'")
        print("2. 或者在运行程序时指定:")
        print("   python ai_lab_repo1.py --semantic-api-key 'your_api_key_here' ...")
