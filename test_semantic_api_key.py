#!/usr/bin/env python3
"""
测试Semantic Scholar API密钥是否有效
"""

import requests

def test_semantic_api_key():
    """测试您的API密钥"""
    
    api_key = "sk-ztnz8SSr0iQSBUzkF10739C09762478c92A47671A731F50f"
    
    print(f"🔍 测试API密钥: {api_key[:15]}...")
    
    headers = {
        'OMINI-API-Model': 'semantic',
        'Authorization': f'Bearer {api_key}',
    }

    params = {
        'query': 'machine learning',
        'fields': 'title,authors,year',
        'limit': 3
    }

    try:
        response = requests.get(
            'http://s2api.ominiai.cn/generalProxy/graph/v1/paper/search', 
            params=params, 
            headers=headers,
            timeout=15
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API密钥有效！")
            if 'data' in data and data['data']:
                print(f"找到 {len(data['data'])} 篇论文")
                first_paper = data['data'][0]
                print(f"第一篇论文: {first_paper.get('title', 'Unknown')}")
                return True
            else:
                print("⚠️  API有效但返回空结果")
                return False
        elif response.status_code == 401:
            print("❌ API密钥无效")
            print(f"响应: {response.text}")
            return False
        elif response.status_code == 429:
            print("⚠️  API速率限制")
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    test_semantic_api_key()
