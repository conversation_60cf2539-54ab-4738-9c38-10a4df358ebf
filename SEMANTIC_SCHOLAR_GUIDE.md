# Semantic Scholar API 集成使用指南

## 概述

本项目已集成了Semantic Scholar API，可以大大增强文献综述功能，并自动生成专业的References章节。

## 功能特性

### 🔍 **增强的文献搜索**
- 通过Semantic Scholar数据库搜索学术论文
- 支持自定义API端点（OMINI代理服务）
- 自动重试和错误处理
- 智能速率限制管理

### 📚 **智能文献综述**
- 自动生成结构化文献综述
- 按引用数排序论文
- 提取关键信息（标题、作者、年份、摘要等）
- 保存搜索结果到Markdown文件

### 📖 **专业References章节**
- 自动生成学术格式的参考文献
- 整合ArXiv和Semantic Scholar的引用
- 符合学术标准的引用格式
- 包含DOI、URL和引用数信息

## 配置方法

### 方法1：环境变量（推荐）

```bash
# 设置API密钥
export SEMANTIC_SCHOLAR_API_KEY="sk-xxxxxxxxx"

# 运行程序
python ai_lab_repo1.py --research-topic "your topic" ...
```

### 方法2：命令行参数

```bash
python ai_lab_repo1.py \
  --research-topic "Data-Driven Optimization of Silicon Extraction" \
  --semantic-api-key "sk-xxxxxxxxx" \
  --api-key "your_gemini_key" \
  --language "中文" \
  --llm-backend "gemini-2.5-flash" \
  --num-papers-lit-review "5" \
  --copilot-mode "true"
```

### 方法3：代码中直接配置

修改 `enhanced_semantic_search.py`：

```python
# 在初始化时直接传入API密钥
search = SemanticScholarSearch(api_key="sk-xxxxxxxxx", use_custom_api=True)
```

## 使用方法

### 在Literature Review阶段

程序运行到Literature Review阶段时，您可以使用以下命令：

1. **SEMANTIC_SEARCH** - 使用Semantic Scholar搜索
   ```
   ```SEMANTIC_SEARCH
   machine learning SHAP explainable AI
   ```

2. **SUMMARY** - 传统ArXiv搜索
   ```
   ```SUMMARY
   silicon extraction fly ash
   ```

3. **PERPLEXITY_REVIEW** - Perplexity AI综述（如果配置了）

### 搜索示例

```python
from enhanced_semantic_search import SemanticScholarSearch

# 初始化
search = SemanticScholarSearch(api_key="sk-xxxxxxxxx")

# 搜索论文
results = search.search_papers("machine learning SHAP", limit=10)

# 生成文献综述
review_text, references = search.generate_literature_review(
    "explainable AI in materials science", 
    num_papers=5
)

# 生成References章节
refs_section = search.generate_references_section()
```

## API配置详情

### 自定义API端点（OMINI代理）

```python
# 配置信息
base_url = "http://s2api.ominiai.cn/generalProxy/graph/v1"
headers = {
    'OMINI-API-Model': 'semantic',
    'Authorization': 'Bearer sk-xxxxxxxxx',
}
```

### 官方API端点

```python
# 配置信息
base_url = "https://api.semanticscholar.org/graph/v1"
headers = {
    'x-api-key': 'your_api_key'
}
```

## 输出文件

### 文献搜索结果
- `research_dir/SEMANTIC_[query]_[timestamp].md` - 文献综述
- `research_dir/semantic_scholar_[query]_[timestamp].json` - 原始搜索结果

### 最终报告
- `research_dir/research_paper.md` - 包含References章节的完整报告
- `research_dir/final_report.md` - 最终报告副本

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   Error: 401 Unauthorized
   ```
   解决：检查API密钥是否正确设置

2. **速率限制**
   ```
   Error: 429 Too Many Requests
   ```
   解决：程序会自动重试，或等待一段时间后重新运行

3. **网络连接问题**
   ```
   Error: Connection timeout
   ```
   解决：检查网络连接，确保可以访问API端点

### 调试模式

运行测试脚本检查配置：

```bash
python test_custom_semantic_api.py
```

## 最佳实践

1. **API密钥安全**
   - 使用环境变量存储API密钥
   - 不要在代码中硬编码密钥
   - 定期轮换API密钥

2. **搜索策略**
   - 使用具体的关键词组合
   - 限制搜索结果数量（5-10篇）
   - 结合多个搜索引擎使用

3. **文献管理**
   - 定期清理搜索结果文件
   - 备份重要的文献综述
   - 验证引用的准确性

## 集成效果

使用Semantic Scholar API后，您的研究报告将包含：

- ✅ 更全面的文献综述
- ✅ 专业的References章节
- ✅ 高质量的学术引用
- ✅ 符合SCI论文标准的格式
- ✅ 自动化的文献管理

这使得生成的报告更像专业的科学文章，提升了研究的学术价值。
