#!/usr/bin/env python3
"""
测试Semantic Scholar API密钥
"""

import requests
import os

def test_api_key(api_key):
    """
    测试API密钥是否有效
    @param api_key: API密钥
    """
    print(f"🔍 测试API密钥: {api_key[:10]}...")
    
    headers = {
        'OMINI-API-Model': 'semantic',
        'Authorization': f'Bearer {api_key}',
    }

    params = {
        'query': 'machine learning',
    }

    try:
        # 测试autocomplete端点
        response = requests.get(
            'http://s2api.ominiai.cn/generalProxy/graph/v1/paper/autocomplete', 
            params=params, 
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API密钥有效！")
            print(f"返回数据: {data}")
            return True
        elif response.status_code == 401:
            print("❌ API密钥无效或已过期")
            return False
        elif response.status_code == 429:
            print("⚠️  API速率限制，但密钥可能有效")
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_search_endpoint(api_key):
    """
    测试搜索端点
    @param api_key: API密钥
    """
    print(f"\n🔍 测试搜索端点...")
    
    headers = {
        'OMINI-API-Model': 'semantic',
        'Authorization': f'Bearer {api_key}',
    }

    params = {
        'query': 'machine learning',
        'fields': 'title,authors,year',
        'limit': 3
    }

    try:
        response = requests.get(
            'http://s2api.ominiai.cn/generalProxy/graph/v1/paper/search', 
            params=params, 
            headers=headers,
            timeout=15
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 搜索端点工作正常！")
            if 'data' in data and data['data']:
                print(f"找到 {len(data['data'])} 篇论文")
                first_paper = data['data'][0]
                print(f"第一篇论文: {first_paper.get('title', 'Unknown')}")
            return True
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Semantic Scholar API密钥测试")
    print("=" * 50)
    
    # 从环境变量获取API密钥
    api_key = os.getenv('SEMANTIC_SCHOLAR_API_KEY')
    
    if not api_key:
        print("❌ 未找到环境变量 SEMANTIC_SCHOLAR_API_KEY")
        print("\n请设置环境变量:")
        print("export SEMANTIC_SCHOLAR_API_KEY='sk-xxxxxxxxx'")
        print("\n或者直接输入API密钥进行测试:")
        api_key = input("请输入您的API密钥: ").strip()
    
    if api_key:
        # 测试autocomplete端点
        if test_api_key(api_key):
            # 如果autocomplete成功，测试搜索端点
            test_search_endpoint(api_key)
        
        print(f"\n📝 如果测试成功，您可以这样使用:")
        print(f"export SEMANTIC_SCHOLAR_API_KEY='{api_key}'")
        print("python ai_lab_repo1.py --research-topic 'your topic' ...")
    else:
        print("❌ 没有提供API密钥")
