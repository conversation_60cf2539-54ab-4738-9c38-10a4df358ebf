# 端到端数据科学项目：有效硅产出预测与关键工艺参数识别

## 项目目标

本项目旨在通过分析生产过程中的各项工艺参数，构建一个能够准确预测“Effective_Silicon”产出的机器学习模型，并利用模型解释性工具（SHAP）识别影响有效硅产出的关键因素，最终形成一份详细的研究报告。

## 数据源

- 数据路径: `/Users/<USER>/Documents/Python/agentlaboratory/data/Si2025_5_4.csv`
- 目标变量: `Effective_Silicon`
- 特征变量: 数据集中的其余所有列

## 执行流程

### 1. 环境设置与数据加载
- **任务**: 导入所有必需的Python库 (pandas, numpy, scikit-learn, matplotlib, seaborn, lazypredict, shap)。
- **任务**: 从指定的CSV文件路径加载数据到pandas DataFrame。
- **任务**: 检查数据基本信息，如形状、数据类型和缺失值。

### 2. 探索性数据分析 (EDA)
- **任务**: 计算数值型特征的描述性统计信息（均值、标准差、中位数等）。
- **任务**: 分析目标变量 `Effective_Silicon` 的分布情况（如绘制直方图和箱线图）。
- **任务**: 绘制特征之间的相关性热力图，初步识别与目标变量高度相关的特征。
- **任务**: 使用`seaborn.pairplot`绘制参数间的交互散点图，可视化特征与特征、特征与目标变量之间的关系。
- **产出**: EDA相关的图表文件（如 `EDA_pairplot.png`, `correlation_heatmap.png`）。

### 3. 数据预处理与特征工程
- **任务**: 分离特征（X）和目标变量（y）。
- **任务**: 识别分类（名义型）特征和数值特征。
- **任务**: 对名义型字符串特征应用独热编码（One-Hot Encoding）。
- **任务**: 将数据集拆分为训练集和测试集。

### 4. 机器学习模型筛选
- **任务**: 使用`lazypredict`库快速训练和评估多种回归模型。
- **任务**: 比较不同模型的性能指标（如R-squared, RMSE, MAE）。
- **任务**: 根据`lazypredict`的结果，选择1-2个表现最佳的模型进行深度分析。
- **产出**: `lazypredict`模型性能对比表格的截图或文本。

### 5. 模型构建与评估
- **任务**: 使用选择的最佳模型（例如XGBoost, RandomForest等）在训练集上进行训练。
- **任务**: 在测试集上评估模型的性能。
- **任务**: 绘制预测值与实际值的对比散点图，以直观评估模型性能。
- **产出**: 预测值vs实际值对比图 (`predicted_vs_actual.png`)。

### 6. 模型解释与特征重要性分析 (SHAP)
- **任务**: 使用SHAP库计算选定模型的SHAP值。
- **任务**: 绘制SHAP摘要图（summary plot），展示全局特征重要性。
- **任务**: 针对最重要的几个特征，绘制SHAP依赖图（dependence plot），以揭示特征如何影响模型输出。
- **产出**: SHAP相关的图表文件（如 `shap_summary_plot.png`, `shap_dependence_plot_featureX.png`）。

### 7. 研究报告撰写
- **任务**: 基于以上所有分析步骤和产出的图表，撰写一份详细的研究论文。
- **论文结构**:
    - **摘要**: 简述项目背景、方法、关键发现和结论。
    - **引言**: 介绍有效硅生产的背景和研究的重要性。
    - **数据与方法**: 描述数据集、预处理步骤和所使用的分析方法（EDA, LazyPredict, SHAP等）。
    - **结果与讨论**:
        - 展示EDA的关键发现。
        - 呈现模型筛选和最终模型的性能。
        - 深入讨论SHAP分析结果，解释关键工艺参数对有效硅产出的影响。
    - **结论**: 总结研究发现，并提出潜在的工艺优化建议。
- **产出**: `research_paper.md` 或 `research_paper.pdf`。