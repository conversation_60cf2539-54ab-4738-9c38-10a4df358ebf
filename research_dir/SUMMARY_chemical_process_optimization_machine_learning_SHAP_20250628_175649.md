Title: An Order-Invariant and Interpretable Hierarchical Dilated Convolution Neural Network for Chemical Fault Detection and Diagnosis
Summary: Fault detection and diagnosis is significant for reducing maintenance costs
and improving health and safety in chemical processes. Convolution neural
network (CNN) is a popular deep learning algorithm with many successful
applications in chemical fault detection and diagnosis tasks. However,
convolution layers in CNN are very sensitive to the order of features, which
can lead to instability in the processing of tabular data. Optimal order of
features result in better performance of CNN models but it is expensive to seek
such optimal order. In addition, because of the encapsulation mechanism of
feature extraction, most CNN models are opaque and have poor interpretability,
thus failing to identify root-cause features without human supervision. These
difficulties inevitably limit the performance and credibility of CNN methods.
In this paper, we propose an order-invariant and interpretable hierarchical
dilated convolution neural network (HDLCNN), which is composed by feature
clustering, dilated convolution and the shapley additive explanations (SHAP)
method. The novelty of HDLCNN lies in its capability of processing tabular data
with features of arbitrary order without seeking the optimal order, due to the
ability to agglomerate correlated features of feature clustering and the large
receptive field of dilated convolution. Then, the proposed method provides
interpretability by including the SHAP values to quantify feature contribution.
Therefore, the root-cause features can be identified as the features with the
highest contribution. Computational experiments are conducted on the Tennessee
Eastman chemical process benchmark dataset. Compared with the other methods,
the proposed HDLCNN-SHAP method achieves better performance on processing
tabular data with features of arbitrary order, detecting faults, and
identifying the root-cause features.
Publication Date: 2023-02-13
Categories: cs.LG
arXiv paper ID: 2302.06243v1

Title: Interpretable SHAP-bounded Bayesian Optimization for Underwater Acoustic Metamaterial Coating Design
Summary: We developed an interpretability informed Bayesian optimization framework to
optimize underwater acoustic coatings based on polyurethane elastomers with
embedded metamaterial features. A data driven model was employed to analyze the
relationship between acoustic performance, specifically sound absorption and
the corresponding design variables. By leveraging SHapley Additive exPlanations
(SHAP), a machine learning interpretability tool, we identified the key
parameters influencing the objective function and gained insights into how
these parameters affect sound absorption. The insights derived from the SHAP
analysis were subsequently used to automatically refine the bounds of the
optimization problem automatically, enabling a more targeted and efficient
exploration of the design space.
  The proposed approach was applied to two polyurethane materials with distinct
hardness levels, resulting in improved optimal solutions compared to those
obtained without SHAP-informed guidance. Notably, these enhancements were
achieved without increasing the number of simulation iterations. Our findings
demonstrate the potential of SHAP to streamline optimization processes by
uncovering hidden parameter relationships and guiding the search toward
promising regions of the design space. This work underscores the effectiveness
of combining interpretability techniques with Bayesian optimization for the
efficient and cost-effective design of underwater acoustic metamaterials under
strict computational constraints and can be generalized towards other materials
and engineering optimization problems.
Publication Date: 2025-05-10
Categories: cs.LG cond-mat.mtrl-sci cs.SD
arXiv paper ID: 2505.06519v1

Title: The XAISuite framework and the implications of explanatory system dissonance
Summary: Explanatory systems make machine learning models more transparent. However,
they are often inconsistent. In order to quantify and isolate possible
scenarios leading to this discrepancy, this paper compares two explanatory
systems, SHAP and LIME, based on the correlation of their respective importance
scores using 14 machine learning models (7 regression and 7 classification) and
4 tabular datasets (2 regression and 2 classification). We make two novel
findings. Firstly, the magnitude of importance is not significant in
explanation consistency. The correlations between SHAP and LIME importance
scores for the most important features may or may not be more variable than the
correlation between SHAP and LIME importance scores averaged across all
features. Secondly, the similarity between SHAP and LIME importance scores
cannot predict model accuracy. In the process of our research, we construct an
open-source library, XAISuite, that unifies the process of training and
explaining models. Finally, this paper contributes a generalized framework to
better explain machine learning models and optimize their performance.
Publication Date: 2023-04-15
Categories: cs.LG cs.AI
arXiv paper ID: 2304.08499v1

Title: Provably Accurate Shapley Value Estimation via Leverage Score Sampling
Summary: Originally introduced in game theory, Shapley values have emerged as a
central tool in explainable machine learning, where they are used to attribute
model predictions to specific input features. However, computing Shapley values
exactly is expensive: for a general model with $n$ features, $O(2^n)$ model
evaluations are necessary. To address this issue, approximation algorithms are
widely used. One of the most popular is the Kernel SHAP algorithm, which is
model agnostic and remarkably effective in practice. However, to the best of
our knowledge, Kernel SHAP has no strong non-asymptotic complexity guarantees.
We address this issue by introducing Leverage SHAP, a light-weight modification
of Kernel SHAP that provides provably accurate Shapley value estimates with
just $O(n n)$ model evaluations. Our approach takes advantage of a
connection between Shapley value estimation and agnostic active learning by
employing leverage score sampling, a powerful regression tool. Beyond
theoretical guarantees, we show that Leverage SHAP consistently outperforms
even the highly optimized implementation of Kernel SHAP available in the
ubiquitous SHAP library [Lundberg & Lee, 2017].
Publication Date: 2024-10-02
Categories: cs.LG cs.AI
arXiv paper ID: 2410.01917v2

Title: An Imprecise SHAP as a Tool for Explaining the Class Probability Distributions under Limited Training Data
Summary: One of the most popular methods of the machine learning prediction
explanation is the SHapley Additive exPlanations method (SHAP). An imprecise
SHAP as a modification of the original SHAP is proposed for cases when the
class probability distributions are imprecise and represented by sets of
distributions. The first idea behind the imprecise SHAP is a new approach for
computing the marginal contribution of a feature, which fulfils the important
efficiency property of Shapley values. The second idea is an attempt to
consider a general approach to calculating and reducing interval-valued Shapley
values, which is similar to the idea of reachable probability intervals in the
imprecise probability theory. A simple special implementation of the general
approach in the form of linear optimization problems is proposed, which is
based on using the Kolmogorov-Smirnov distance and imprecise contamination
models. Numerical examples with synthetic and real data illustrate the
imprecise SHAP.
Publication Date: 2021-06-16
Categories: cs.LG stat.ML
arXiv paper ID: 2106.09111v1