Title: Balanced background and explanation data are needed in explaining deep learning models with SHAP: An empirical study on clinical decision making
Summary: Objective: Shapley additive explanations (SHAP) is a popular post-hoc
technique for explaining black box models. While the impact of data imbalance
on predictive models has been extensively studied, it remains largely unknown
with respect to SHAP-based model explanations. This study sought to investigate
the effects of data imbalance on SHAP explanations for deep learning models,
and to propose a strategy to mitigate these effects. Materials and Methods: We
propose to adjust class distributions in the background and explanation data in
SHAP when explaining black box models. Our data balancing strategy is to
compose background data and explanation data with an equal distribution of
classes. To evaluate the effects of data adjustment on model explanation, we
propose to use the beeswarm plot as a qualitative tool to identify "abnormal"
explanation artifacts, and quantitatively test the consistency between variable
importance and prediction power. We demonstrated our proposed approach in an
empirical study that predicted inpatient mortality using the Medical
Information Mart for Intensive Care (MIMIC-III) data and a multilayer
perceptron. Results: Using the data balancing strategy would allow us to reduce
the number of the artifacts in the beeswarm plot, thus mitigating the negative
effects of data imbalance. Additionally, with the balancing strategy, the
top-ranked variables from the corresponding importance ranking demonstrated
improved discrimination power. Discussion and Conclusion: Our findings suggest
that balanced background and explanation data could help reduce the noise in
explanation results induced by skewed data distribution and improve the
reliability of variable importance ranking. Furthermore, these balancing
procedures improve the potential of SHAP in identifying patients with abnormal
characteristics in clinical applications.
Publication Date: 2022-06-08
Categories: cs.LG cs.HC
arXiv paper ID: 2206.04050v1

Title: Modelling of nucleate pool boiling on coated substrates using machine learning and empirical approaches
Summary: Surface modification results in substantial improvement in pool boiling heat
transfer. Thin film-coated and porous-coated substrates, through different
materials and techniques, significantly boost heat transfer through increased
nucleation due to the presence of micro-cavities on the surface. The existing
models and empirical correlations for boiling on these coated surfaces are
constrained by specific operating conditions and parameter ranges and are hence
limited by their prediction accuracy. This study focuses on developing an
accurate and reliable Machine Learning (ML) model by effectively capturing the
actual relationship between the influencing variables. Various ML algorithms
have been evaluated on the thin film-coated and porous-coated datasets amassed
from different studies. The CatBoost model demonstrated the best prediction
accuracy after cross-validation and hyperparameter tuning. For the optimized
CatBoost model, SHAP analysis has been carried out to identify the prominent
influencing parameters and interpret the impact of parameter variation on the
target variable. This model interpretation clearly justifies the decisions
behind the model predictions, making it a robust model for the prediction of
nucleate boiling Heat Transfer Coefficient (HTC) on coated surfaces. Finally,
the existing empirical correlations have been assessed, and new correlations
have been proposed to predict the HTC on these surfaces with the inclusion of
influential parameters identified through SHAP interpretation.
  Keywords: Pool boiling, Thin film-coated, Porous-coated, Heat transfer
coefficient, Machine learning, CatBoost, SHAP analysis
Publication Date: 2024-09-12
Categories: physics.app-ph cond-mat.mtrl-sci
arXiv paper ID: 2409.07811v1

Title: SHAP for additively modeled features in a boosted trees model
Summary: An important technique to explore a black-box machine learning (ML) model is
called SHAP (SHapley Additive exPlanation). SHAP values decompose predictions
into contributions of the features in a fair way. We will show that for a
boosted trees model with some or all features being additively modeled, the
SHAP dependence plot of such a feature corresponds to its partial dependence
plot up to a vertical shift. We illustrate the result with XGBoost.
Publication Date: 2022-07-29
Categories: stat.ML cs.LG
arXiv paper ID: 2207.14490v1

Title: Computing SHAP Efficiently Using Model Structure Information
Summary: SHAP (SHapley Additive exPlanations) has become a popular method to attribute
the prediction of a machine learning model on an input to its features. One
main challenge of SHAP is the computation time. An exact computation of Shapley
values requires exponential time complexity. Therefore, many approximation
methods are proposed in the literature. In this paper, we propose methods that
can compute SHAP exactly in polynomial time or even faster for SHAP definitions
that satisfy our additivity and dummy assumptions (eg, kernal SHAP and baseline
SHAP). We develop different strategies for models with different levels of
model structure information: known functional decomposition, known order of
model (defined as highest order of interaction in the model), or unknown order.
For the first case, we demonstrate an additive property and a way to compute
SHAP from the lower-order functional components. For the second case, we derive
formulas that can compute SHAP in polynomial time. Both methods yield exact
SHAP results. Finally, if even the order of model is unknown, we propose an
iterative way to approximate Shapley values. The three methods we propose are
computationally efficient when the order of model is not high which is
typically the case in practice. We compare with sampling approach proposed in
Castor & Gomez (2008) using simulation studies to demonstrate the efficacy of
our proposed methods.
Publication Date: 2023-09-05
Categories: stat.ML cs.LG
arXiv paper ID: 2309.02417v1

Title: Experimental exploration of ErB$_2$ and SHAP analysis on a machine-learned model of magnetocaloric materials for materials design
Summary: Stimulated by a recent report of a giant magnetocaloric effect in HoB$_2$
found via machine-learning predictions, we have explored the magnetocaloric
properties of a related compound ErB$_2$, that has remained the last
ferromagnetic material among the rare-earth diboride (REB$_2$) family with
unreported magnetic entropy change |{}SM|. The evaluated $| S_M|$
at field change of 5 T in ErB$_2$ turned out to be as high as 26.1 (J kg$^{-1}$
K$^{-1}$) around the ferromagnetic transition (${T_C}$) of 14 K. In this
series, HoB$_2$ is found to be the material with the largest $| S_M|$ as
the model predicted, while the predicted values showed a deviation with a
systematic error compared to the experimental values. Through a coalition
analysis using SHAP, we explore how this rare-earth dependence and the
deviation in the prediction are deduced in the model. We further discuss how
SHAP analysis can be useful in clarifying favorable combinations of constituent
atoms through the machine-learned model with compositional descriptors. This
analysis helps us to perform materials design with aid of machine learning of
materials data.
Publication Date: 2023-06-27
Categories: cond-mat.mtrl-sci
arXiv paper ID: 2306.15153v1