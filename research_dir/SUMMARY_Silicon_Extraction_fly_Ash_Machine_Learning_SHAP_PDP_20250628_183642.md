Title: Conditional expectation network for SHAP
Summary: A very popular model-agnostic technique for explaining predictive models is
the SHapley Additive exPlanation (SHAP). The two most popular versions of SHAP
are a conditional expectation version and an unconditional expectation version
(the latter is also known as interventional SHAP). Except for tree-based
methods, usually the unconditional version is used (for computational reasons).
We provide a (surrogate) neural network approach which allows us to efficiently
calculate the conditional version for both neural networks and other regression
models, and which properly considers the dependence structure in the feature
components. This proposal is also useful to provide drop1 and anova analyses in
complex regression models which are similar to their generalized linear model
(GLM) counterparts, and we provide a partial dependence plot (PDP) counterpart
that considers the right dependence structure in the feature components.
Publication Date: 2023-07-20
Categories: cs.LG cs.CE stat.AP stat.ML 62J10, 62J12 I.6.4; I.2.6; G.3
arXiv paper ID: 2307.10654v1

Title: iPDP: On Partial Dependence Plots in Dynamic Modeling Scenarios
Summary: Post-hoc explanation techniques such as the well-established partial
dependence plot (PDP), which investigates feature dependencies, are used in
explainable artificial intelligence (XAI) to understand black-box machine
learning models. While many real-world applications require dynamic models that
constantly adapt over time and react to changes in the underlying distribution,
XAI, so far, has primarily considered static learning environments, where
models are trained in a batch mode and remain unchanged. We thus propose a
novel model-agnostic XAI framework called incremental PDP (iPDP) that extends
on the PDP to extract time-dependent feature effects in non-stationary learning
environments. We formally analyze iPDP and show that it approximates a
time-dependent variant of the PDP that properly reacts to real and virtual
concept drift. The time-sensitivity of iPDP is controlled by a single smoothing
parameter, which directly corresponds to the variance and the approximation
error of iPDP in a static learning environment. We illustrate the efficacy of
iPDP by showcasing an example application for drift detection and conducting
multiple experiments on real-world and synthetic data sets and streams.
Publication Date: 2023-06-13
Categories: cs.LG cs.AI eess.SP
arXiv paper ID: 2306.07775v1

Title: PiML Toolbox for Interpretable Machine Learning Model Development and Diagnostics
Summary: PiML (read $$-ML, /`pai`em`el/) is an integrated and open-access Python
toolbox for interpretable machine learning model development and model
diagnostics. It is designed with machine learning workflows in both low-code
and high-code modes, including data pipeline, model training and tuning, model
interpretation and explanation, and model diagnostics and comparison. The
toolbox supports a growing list of interpretable models (e.g. GAM, GAMI-Net,
XGB1/XGB2) with inherent local and/or global interpretability. It also supports
model-agnostic explainability tools (e.g. PFI, PDP, LIME, SHAP) and a powerful
suite of model-agnostic diagnostics (e.g. weakness, reliability, robustness,
resilience, fairness). Integration of PiML models and tests to existing MLOps
platforms for quality assurance are enabled by flexible high-code APIs.
Furthermore, PiML toolbox comes with a comprehensive user guide and hands-on
examples, including the applications for model development and validation in
banking. The project is available at
https://github.com/SelfExplainML/PiML-Toolbox.
Publication Date: 2023-05-07
Categories: cs.LG
arXiv paper ID: 2305.04214v3

Title: PDP: Parameter-free Differentiable Pruning is All You Need
Summary: DNN pruning is a popular way to reduce the size of a model, improve the
inference latency, and minimize the power consumption on DNN accelerators.
However, existing approaches might be too complex, expensive or ineffective to
apply to a variety of vision/language tasks, DNN architectures and to honor
structured pruning constraints. In this paper, we propose an efficient yet
effective train-time pruning scheme, Parameter-free Differentiable Pruning
(PDP), which offers state-of-the-art qualities in model size, accuracy, and
training cost. PDP uses a dynamic function of weights during training to
generate soft pruning masks for the weights in a parameter-free manner for a
given pruning target. While differentiable, the simplicity and efficiency of
PDP make it universal enough to deliver state-of-the-art
random/structured/channel pruning results on various vision and natural
language tasks. For example, for MobileNet-v1, PDP can achieve 68.2% top-1
ImageNet1k accuracy at 86.6% sparsity, which is 1.7% higher accuracy than those
from the state-of-the-art algorithms. Also, PDP yields over 83.1% accuracy on
Multi-Genre Natural Language Inference with 90% sparsity for BERT, while the
next best from the existing techniques shows 81.5% accuracy. In addition, PDP
can be applied to structured pruning, such as N:M pruning and channel pruning.
For 1:4 structured pruning of ResNet18, PDP improved the top-1 ImageNet1k
accuracy by over 3.6% over the state-of-the-art. For channel pruning of
ResNet50, PDP reduced the top-1 ImageNet1k accuracy by 0.6% from the
state-of-the-art.
Publication Date: 2023-05-18
Categories: cs.LG cs.AI cs.CV
arXiv paper ID: 2305.11203v3

Title: Surface modification of fly ash by mechano-chemical treatment
Summary: Fly ash (FA), as an industry by-product has attracted much attention as a
suitable supplier of silicon (Si) and aluminum (Al) in preparation of
geopolymer for the sustainable environment and material science applications.
Here, the effect of mechano-chemical (MC) treatment for surface modification of
FA powder was systemically investigated by analyzing the size, surface
morphology, crystal structure and dissolubility of Si and Al ions in alkali
solutions. The dissolution dynamic as a function of MC treatment time is
discussed in details and concluded with a recombination model of "grinding
effect" and "activation effect", which can be well correlated with the change
of surface morphology and crystal structure, respectively.
Publication Date: 2022-02-03
Categories: cond-mat.mtrl-sci
arXiv paper ID: 2202.01779v1