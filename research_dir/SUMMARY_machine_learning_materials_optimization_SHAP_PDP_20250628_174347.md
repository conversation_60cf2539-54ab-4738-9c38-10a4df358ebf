Title: Interpretable SHAP-bounded Bayesian Optimization for Underwater Acoustic Metamaterial Coating Design
Summary: We developed an interpretability informed Bayesian optimization framework to
optimize underwater acoustic coatings based on polyurethane elastomers with
embedded metamaterial features. A data driven model was employed to analyze the
relationship between acoustic performance, specifically sound absorption and
the corresponding design variables. By leveraging SHapley Additive exPlanations
(SHAP), a machine learning interpretability tool, we identified the key
parameters influencing the objective function and gained insights into how
these parameters affect sound absorption. The insights derived from the SHAP
analysis were subsequently used to automatically refine the bounds of the
optimization problem automatically, enabling a more targeted and efficient
exploration of the design space.
  The proposed approach was applied to two polyurethane materials with distinct
hardness levels, resulting in improved optimal solutions compared to those
obtained without SHAP-informed guidance. Notably, these enhancements were
achieved without increasing the number of simulation iterations. Our findings
demonstrate the potential of SHAP to streamline optimization processes by
uncovering hidden parameter relationships and guiding the search toward
promising regions of the design space. This work underscores the effectiveness
of combining interpretability techniques with Bayesian optimization for the
efficient and cost-effective design of underwater acoustic metamaterials under
strict computational constraints and can be generalized towards other materials
and engineering optimization problems.
Publication Date: 2025-05-10
Categories: cs.LG cond-mat.mtrl-sci cs.SD
arXiv paper ID: 2505.06519v1

Title: Explaining Hyperparameter Optimization via Partial Dependence Plots
Summary: Automated hyperparameter optimization (HPO) can support practitioners to
obtain peak performance in machine learning models. However, there is often a
lack of valuable insights into the effects of different hyperparameters on the
final model performance. This lack of explainability makes it difficult to
trust and understand the automated HPO process and its results. We suggest
using interpretable machine learning (IML) to gain insights from the
experimental data obtained during HPO with Bayesian optimization (BO). BO tends
to focus on promising regions with potential high-performance configurations
and thus induces a sampling bias. Hence, many IML techniques, such as the
partial dependence plot (PDP), carry the risk of generating biased
interpretations. By leveraging the posterior uncertainty of the BO surrogate
model, we introduce a variant of the PDP with estimated confidence bands. We
propose to partition the hyperparameter space to obtain more confident and
reliable PDPs in relevant sub-regions. In an experimental study, we provide
quantitative evidence for the increased quality of the PDPs within sub-regions.
Publication Date: 2021-11-08
Categories: cs.LG stat.ML
arXiv paper ID: 2111.04820v2

Title: Provably Accurate Shapley Value Estimation via Leverage Score Sampling
Summary: Originally introduced in game theory, Shapley values have emerged as a
central tool in explainable machine learning, where they are used to attribute
model predictions to specific input features. However, computing Shapley values
exactly is expensive: for a general model with $n$ features, $O(2^n)$ model
evaluations are necessary. To address this issue, approximation algorithms are
widely used. One of the most popular is the Kernel SHAP algorithm, which is
model agnostic and remarkably effective in practice. However, to the best of
our knowledge, Kernel SHAP has no strong non-asymptotic complexity guarantees.
We address this issue by introducing Leverage SHAP, a light-weight modification
of Kernel SHAP that provides provably accurate Shapley value estimates with
just $O(n n)$ model evaluations. Our approach takes advantage of a
connection between Shapley value estimation and agnostic active learning by
employing leverage score sampling, a powerful regression tool. Beyond
theoretical guarantees, we show that Leverage SHAP consistently outperforms
even the highly optimized implementation of Kernel SHAP available in the
ubiquitous SHAP library [Lundberg & Lee, 2017].
Publication Date: 2024-10-02
Categories: cs.LG cs.AI
arXiv paper ID: 2410.01917v2

Title: Public Access Defibrillator Deployment for Cardiac Arrests: A Learn-Then-Optimize Approach with SHAP-based Interpretable Analytics
Summary: Out-of-hospital cardiac arrest (OHCA) survival rates remain extremely low due
to challenges in the timely accessibility of medical devices. Therefore,
effective deployment of automated external defibrillators (AED) can
significantly increase survival rates. Precise and interpretable predictions of
OHCA occurrences provide a solid foundation for efficient and robust AED
deployment optimization. This study develops a novel learn-then-optimize
approach, integrating three key components: a machine learning prediction
model, SHAP-based interpretable analytics, and a SHAP-guided integer
programming (SIP) model. The machine learning model is trained utilizing only
geographic data as inputs to overcome data availability obstacles, and its
strong predictive performance validates the feasibility of interpretation.
Furthermore, the SHAP model elaborates on the contribution of each geographic
feature to the OHCA occurrences. Finally, an integer programming model is
formulated for optimizing AED deployment, incorporating SHAP-weighted OHCA
densities. Various numerical experiments are conducted across different
settings. Based on comparative and sensitive analysis, the optimization effect
of our approach is verified and valuable insights are derived to provide
substantial support for theoretical extension and practical implementation.
Publication Date: 2025-01-01
Categories: math.OC
arXiv paper ID: 2501.00819v2

Title: An Imprecise SHAP as a Tool for Explaining the Class Probability Distributions under Limited Training Data
Summary: One of the most popular methods of the machine learning prediction
explanation is the SHapley Additive exPlanations method (SHAP). An imprecise
SHAP as a modification of the original SHAP is proposed for cases when the
class probability distributions are imprecise and represented by sets of
distributions. The first idea behind the imprecise SHAP is a new approach for
computing the marginal contribution of a feature, which fulfils the important
efficiency property of Shapley values. The second idea is an attempt to
consider a general approach to calculating and reducing interval-valued Shapley
values, which is similar to the idea of reachable probability intervals in the
imprecise probability theory. A simple special implementation of the general
approach in the form of linear optimization problems is proposed, which is
based on using the Kolmogorov-Smirnov distance and imprecise contamination
models. Numerical examples with synthetic and real data illustrate the
imprecise SHAP.
Publication Date: 2021-06-16
Categories: cs.LG stat.ML
arXiv paper ID: 2106.09111v1