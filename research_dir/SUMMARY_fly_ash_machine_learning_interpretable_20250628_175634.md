Title: Classification of volcanic ash particles using a convolutional neural network and probability
Summary: Analyses of volcanic ash are typically performed either by qualitatively
classifying ash particles by eye or by quantitatively parameterizing its shape
and texture. While complex shapes can be classified through qualitative
analyses, the results are subjective due to the difficulty of categorizing
complex shapes into a single class. Although quantitative analyses are
objective, selection of shape parameters is required. Here, we applied a
convolutional neural network (CNN) for the classification of volcanic ash.
First, we defined four basal particle shapes (blocky, vesicular, elongated,
rounded) generated by different eruption mechanisms (e.g., brittle
fragmentation), and then trained the CNN using particles composed of only one
basal shape. The CNN could recognize the basal shapes with over 90% accuracy.
Using the trained network, we classified ash particles composed of multiple
basal shapes based on the output of the network, which can be interpreted as a
mixing ratio of the four basal shapes. Clustering of samples by the averaged
probabilities and the intensity is consistent with the eruption type. The
mixing ratio output by the CNN can be used to quantitatively classify complex
shapes in nature without categorizing forcibly and without the need for shape
parameters, which may lead to a new taxonomy.
Publication Date: 2018-05-31
Categories: physics.geo-ph cs.CV cs.LG
arXiv paper ID: 1805.12353v1

Title: Hard ASH: Sparsity and the right optimizer make a continual learner
Summary: In class incremental learning, neural networks typically suffer from
catastrophic forgetting. We show that an MLP featuring a sparse activation
function and an adaptive learning rate optimizer can compete with established
regularization techniques in the Split-MNIST task. We highlight the
effectiveness of the Adaptive SwisH (ASH) activation function in this context
and introduce a novel variant, Hard Adaptive SwisH (Hard ASH) to further
enhance the learning retention.
Publication Date: 2024-04-26
Categories: cs.LG cs.CV
arXiv paper ID: 2404.17651v1

Title: Machine Learning-based Prediction of Porosity for Concrete Containing Supplementary Cementitious Materials
Summary: Porosity has been identified as the key indicator of the durability
properties of concrete exposed to aggressive environments. This paper applies
ensemble learning to predict porosity of high-performance concrete containing
supplementary cementitious materials. The concrete samples utilized in this
study are characterized by eight composition features including w/b ratio,
binder content, fly ash, GGBS, superplasticizer, coarse/fine aggregate ratio,
curing condition and curing days. The assembled database consists of 240 data
records, featuring 74 unique concrete mixture designs. The proposed machine
learning algorithms are trained on 180 observations (75%) chosen randomly from
the data set and then tested on the remaining 60 observations (25%). The
numerical experiments suggest that the regression tree ensembles can accurately
predict the porosity of concrete from its mixture compositions. Gradient
boosting trees generally outperforms random forests in terms of prediction
accuracy. For random forests, the out-of-bag error based hyperparameter tuning
strategy is found to be much more efficient than k-Fold Cross-Validation.
Publication Date: 2021-12-13
Categories: cs.LG cs.CE
arXiv paper ID: 2112.07353v1

Title: Unsupervised detection of ash dieback disease (Hymenoscyphus fraxineus) using diffusion-based hyperspectral image clustering
Summary: Ash dieback (Hymenoscyphus fraxineus) is an introduced fungal disease that is
causing the widespread death of ash trees across Europe. Remote sensing
hyperspectral images encode rich structure that has been exploited for the
detection of dieback disease in ash trees using supervised machine learning
techniques. However, to understand the state of forest health at
landscape-scale, accurate unsupervised approaches are needed. This article
investigates the use of the unsupervised Diffusion and VCA-Assisted Image
Segmentation (D-VIS) clustering algorithm for the detection of ash dieback
disease in a forest site near Cambridge, United Kingdom. The unsupervised
clustering presented in this work has high overlap with the supervised
classification of previous work on this scene (overall accuracy = 71%). Thus,
unsupervised learning may be used for the remote detection of ash dieback
disease without the need for expert labeling.
Publication Date: 2022-04-19
Categories: cs.CV cs.LG stat.AP
arXiv paper ID: 2204.09041v1

Title: Extremely Simple Activation Shaping for Out-of-Distribution Detection
Summary: The separation between training and deployment of machine learning models
implies that not all scenarios encountered in deployment can be anticipated
during training, and therefore relying solely on advancements in training has
its limits. Out-of-distribution (OOD) detection is an important area that
stress-tests a model's ability to handle unseen situations: Do models know when
they don't know? Existing OOD detection methods either incur extra training
steps, additional data or make nontrivial modifications to the trained network.
In contrast, in this work, we propose an extremely simple, post-hoc, on-the-fly
activation shaping method, ASH, where a large portion (e.g. 90%) of a sample's
activation at a late layer is removed, and the rest (e.g. 10%) simplified or
lightly adjusted. The shaping is applied at inference time, and does not
require any statistics calculated from training data. Experiments show that
such a simple treatment enhances in-distribution and out-of-distribution
distinction so as to allow state-of-the-art OOD detection on ImageNet, and does
not noticeably deteriorate the in-distribution accuracy. Video, animation and
code can be found at: https://andrijazz.github.io/ash
Publication Date: 2022-09-20
Categories: cs.LG cs.CV
arXiv paper ID: 2209.09858v2