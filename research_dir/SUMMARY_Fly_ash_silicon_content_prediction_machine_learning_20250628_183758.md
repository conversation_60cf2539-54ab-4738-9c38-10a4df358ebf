Title: Machine Learning-based Prediction of Porosity for Concrete Containing Supplementary Cementitious Materials
Summary: Porosity has been identified as the key indicator of the durability
properties of concrete exposed to aggressive environments. This paper applies
ensemble learning to predict porosity of high-performance concrete containing
supplementary cementitious materials. The concrete samples utilized in this
study are characterized by eight composition features including w/b ratio,
binder content, fly ash, GGBS, superplasticizer, coarse/fine aggregate ratio,
curing condition and curing days. The assembled database consists of 240 data
records, featuring 74 unique concrete mixture designs. The proposed machine
learning algorithms are trained on 180 observations (75%) chosen randomly from
the data set and then tested on the remaining 60 observations (25%). The
numerical experiments suggest that the regression tree ensembles can accurately
predict the porosity of concrete from its mixture compositions. Gradient
boosting trees generally outperforms random forests in terms of prediction
accuracy. For random forests, the out-of-bag error based hyperparameter tuning
strategy is found to be much more efficient than k-Fold Cross-Validation.
Publication Date: 2021-12-13
Categories: cs.LG cs.CE
arXiv paper ID: 2112.07353v1

Title: Stochastic Adaptive Activation Function
Summary: The simulation of human neurons and neurotransmission mechanisms has been
realized in deep neural networks based on the theoretical implementations of
activation functions. However, recent studies have reported that the threshold
potential of neurons exhibits different values according to the locations and
types of individual neurons, and that the activation functions have limitations
in terms of representing this variability. Therefore, this study proposes a
simple yet effective activation function that facilitates different thresholds
and adaptive activations according to the positions of units and the contexts
of inputs. Furthermore, the proposed activation function mathematically
exhibits a more generalized form of Swish activation function, and thus we
denoted it as Adaptive SwisH (ASH). ASH highlights informative features that
exhibit large values in the top percentiles in an input, whereas it rectifies
low values. Most importantly, ASH exhibits trainable, adaptive, and
context-aware properties compared to other activation functions. Furthermore,
ASH represents general formula of the previously studied activation function
and provides a reasonable mathematical background for the superior performance.
To validate the effectiveness and robustness of ASH, we implemented ASH into
many deep learning models for various tasks, including classification,
detection, segmentation, and image generation. Experimental analysis
demonstrates that our activation function can provide the benefits of more
accurate prediction and earlier convergence in many deep learning applications.
Publication Date: 2022-10-21
Categories: cs.LG cs.AI cs.NE
arXiv paper ID: 2210.11672v1

Title: Surface modification of fly ash by mechano-chemical treatment
Summary: Fly ash (FA), as an industry by-product has attracted much attention as a
suitable supplier of silicon (Si) and aluminum (Al) in preparation of
geopolymer for the sustainable environment and material science applications.
Here, the effect of mechano-chemical (MC) treatment for surface modification of
FA powder was systemically investigated by analyzing the size, surface
morphology, crystal structure and dissolubility of Si and Al ions in alkali
solutions. The dissolution dynamic as a function of MC treatment time is
discussed in details and concluded with a recombination model of "grinding
effect" and "activation effect", which can be well correlated with the change
of surface morphology and crystal structure, respectively.
Publication Date: 2022-02-03
Categories: cond-mat.mtrl-sci
arXiv paper ID: 2202.01779v1

Title: Hard ASH: Sparsity and the right optimizer make a continual learner
Summary: In class incremental learning, neural networks typically suffer from
catastrophic forgetting. We show that an MLP featuring a sparse activation
function and an adaptive learning rate optimizer can compete with established
regularization techniques in the Split-MNIST task. We highlight the
effectiveness of the Adaptive SwisH (ASH) activation function in this context
and introduce a novel variant, Hard Adaptive SwisH (Hard ASH) to further
enhance the learning retention.
Publication Date: 2024-04-26
Categories: cs.LG cs.CV
arXiv paper ID: 2404.17651v1

Title: Whose Preferences? Differences in Fairness Preferences and Their Impact on the Fairness of AI Utilizing Human Feedback
Summary: There is a growing body of work on learning from human feedback to align
various aspects of machine learning systems with human values and preferences.
We consider the setting of fairness in content moderation, in which human
feedback is used to determine how two comments -- referencing different
sensitive attribute groups -- should be treated in comparison to one another.
With a novel dataset collected from Prolific and MTurk, we find significant
gaps in fairness preferences depending on the race, age, political stance,
educational level, and LGBTQ+ identity of annotators. We also demonstrate that
demographics mentioned in text have a strong influence on how users perceive
individual fairness in moderation. Further, we find that differences also exist
in downstream classifiers trained to predict human preferences. Finally, we
observe that an ensemble, giving equal weight to classifiers trained on
annotations from different demographics, performs better for different
demographic intersections; compared to a single classifier that gives equal
weight to each annotation.
Publication Date: 2024-06-09
Categories: cs.LG cs.AI cs.CL cs.CY
arXiv paper ID: 2406.05902v1