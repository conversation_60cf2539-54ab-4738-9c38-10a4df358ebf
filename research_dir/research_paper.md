# Research Report: 基于可解释机器学习与条件期望网络的粉煤灰提硅工艺优化研究

**作者:** Xiao

## 1. 摘要

粉煤灰作为一种大规模工业废弃物，其高值化利用，特别是硅元素的有效提取，对于环境保护和资源循环至关重要。粉煤灰提硅过程的复杂性及多因素耦合特性，使得传统优化方法面临挑战。本研究旨在通过集成先进机器学习（ML）与可解释性人工智能（XAI）技术，精准预测粉煤灰中有效硅的提取含量，并深入理解影响提取效率的关键因素及其相互作用机制。

研究分为两个递进阶段。**第一阶段**，我们系统性地训练并评估了K近邻（KNN）、随机森林（RF）、XGBoost、LightGBM、CatBoost、多层感知机（MLP）以及Stacking集成模型，用于预测有效硅含量。实验结果表明，Stacking集成模型取得了最佳预测性能，在独立测试集上R²高达0.8826，平均绝对误差（MAE）为0.0165，均方根误差（RMSE）为0.0204，展现出卓越的准确性和泛化能力。鉴于XGBoost在保持高性能的同时具备良好的单一模型可解释性，我们选择其作为初步XAI分析的重点。利用SHapley Additive exPlanation (SHAP) 值和偏依赖图（Partial Dependence Plots, PDPs），我们成功识别并量化了初始二氧化硅（SiO₂）含量、氧化铝（Al₂O₃）含量、反应温度和酸/碱浓度等核心因素对硅提取的独立影响方向与非线性模式。

**第二阶段**，为克服传统XAI方法在处理强特征依赖性时可能产生的误导，我们创新性地引入了基于条件期望网络（Conditional Expectation Network, CEN）的框架（Richman & Wüthrich, 2023），以计算更准确的条件SHAP值并生成边缘条件期望图（Marginal Conditional Expectation Plots, MCEP）。CEN通过学习原始模型在给定部分观测特征条件下的期望输出，避免了对训练数据中不合理特征组合的外推。通过对最佳Stacking模型进行多层次解释，包括基础学习器与元学习器的SHAP分析，我们深入揭示了集成模型内部的决策逻辑。虽然本报告在结果章节中主要展示了传统PDP，但方法论部分已阐明CEN在提供更符合实际物理化学过程、考虑特征依赖性的解释方面的独特优势。

本研究的主要贡献在于：1) 构建并验证了高精度机器学习预测模型，为粉煤灰提硅提供了坚实的数据驱动基础；2) 初步量化并直观揭示了关键影响因素的独立作用模式；3) 引入并探讨了条件期望网络及其在解决特征依赖性问题上的潜力，为复杂材料提取过程的深度可解释性分析提供了新范式；4) 提出了分层解释策略，全面剖析了复杂集成模型的内部机制，为工业工艺优化和未来科学发现提供了更精准、更具指导意义的洞察。
## 2. 引言

粉煤灰，作为燃煤发电厂产生的大宗工业副产品，全球每年产量高达数十亿吨，其大规模堆积不仅对土地资源造成巨大压力，还可能引发一系列环境问题，如扬尘污染和重金属淋溶。然而，粉煤灰并非简单的废弃物，它富含硅（SiO₂）、铝（Al₂O₃）等高价值氧化物，使其成为潜在的“二次资源”。通过对粉煤灰进行高效的硅元素提取与回收，不仅能够有效缓解环境压力，实现固体废物的资源化、高值化利用，还能为半导体、光伏、陶瓷、吸附剂等高附加值产业提供关键的硅基原材料，符合当前可持续发展和循环经济的战略需求。本研究正是基于这一背景，致力于利用先进的机器学习（ML）与可解释性人工智能（XAI）技术，对粉煤灰中有效硅的提取含量进行精准预测，并深入剖析影响提取效率的核心因素及其复杂的相互作用机制，旨在为粉煤灰的高效、清洁提硅工艺提供数据驱动的优化策略和坚实的理论依据。

粉煤灰中硅元素的化学浸出或提取过程是一个高度复杂的物理化学系统，其效率受到多方面因素的耦合影响。这些因素不仅包括粉煤灰自身的内在特性，例如主要化学成分（如SiO₂、Al₂O₃、Fe₂O₃、CaO、MgO、K₂O、Na₂O、TiO₂等元素的百分比含量）、物理形貌（如比表面积、粒度分布）和烧失量，还涵盖了提取工艺的关键操作参数，如酸/碱的种类及浓度、反应温度、反应时间、液固比和搅拌速度等。这些参数之间往往存在显著的非线性关系和复杂的相互依赖性。例如，粉煤灰的初始硅铝比（SiO₂/Al₂O₃）被认为能够显著影响其在特定酸碱条件下的溶解行为和硅的提取率 (arXiv 1111.6847v1; arXiv 2202.01779v1)。传统的工艺优化方法，如单因素实验或正交实验设计，往往难以全面捕捉和量化这些多因素间的复杂耦合效应，导致优化过程耗时、耗力且可能无法达到全局最优。近年来，机器学习，特别是集成学习模型（如梯度提升树和随机森林），在处理高维、非线性、强交互的数据集方面展现出卓越的性能，已在材料科学与工程领域获得广泛应用，例如用于预测混凝土的孔隙率 (arXiv 2112.07353v1)。然而，这些高性能模型通常被视为“黑箱”，其内部决策逻辑和对特定预测结果的贡献机制对人类观察者而言是不透明的。在需要深入理解和优化复杂物理化学过程的科学研究和工业实践中，这种不透明性构成了重大挑战。因此，如何有效地“打开”这些黑箱，提取模型决策背后的可解释性洞察，已成为推动机器学习在复杂材料提取和材料设计领域广泛应用的关键瓶颈。

为有效应对上述挑战，本研究提出并实施了一套集成高精度机器学习预测与先进可解释性人工智能（XAI）工具的综合框架。我们的研究工作分为两个相互关联且递进的实验阶段。在**第一阶段**，我们致力于构建一个稳健的机器学习模型来准确预测粉煤灰中有效硅的提取含量。为此，我们系统性地训练并评估了多种先进的回归模型，包括LightGBM、XGBoost、CatBoost、随机森林（Random Forest）、K近邻（K-Nearest Neighbors）、多层感知机（MLP）以及一个由多个基础模型组成的Stacking集成模型。通过全面的性能评估，Stacking集成模型在独立的测试集上展现出卓越的预测能力，取得了最高的R²分数，达到惊人的0.8826。同时，其平均绝对误差（MAE）仅为0.0165，均方根误差（RMSE）为0.0203，这些指标显著表明了模型预测的准确性和泛化能力。考虑到在保持极高预测性能的同时兼顾模型的内在可解释性，XGBoost模型被选作初步解释性分析的重点对象（其测试集R²为0.8783，MAE为0.0170）。我们利用业界广泛接受的SHapley Additive exPlanation (SHAP) 值和偏依赖图（Partial Dependence Plots, PDPs）对XGBoost模型进行了解释。这些初步分析成功地识别并量化了如粉煤灰中初始SiO₂含量、Al₂O₃含量、反应温度和酸/碱浓度等关键因素对硅提取的独立影响方向和模式。

然而，传统的SHAP和PDP方法在处理数据集中存在的强特征依赖性（即特征之间存在显著相关性）时可能产生误导性的解释。它们通常假设特征是独立的，这在化学反应和材料组分中往往不成立。为了克服这一局限性，**第二阶段**引入了更先进的可解释性方法。我们借鉴并应用了基于条件期望网络（Conditional Expectation Network, CEN）的创新框架 (arXiv:2307.10654v1)，以计算考虑特征依赖关系的条件SHAP值，并生成边缘条件期望图（Marginal Conditional Expectation Plot, MCEP）。CEN通过学习在给定部分观测特征条件下的模型输出的条件期望，能够更准确地反映特定特征在实际数据分布中对预测结果的真实影响，避免了传统方法在稀疏或不合理特征组合区域进行外推的缺陷。通过对传统SHAP/PDP与条件SHAP/MCEP的对比分析，我们突出了后者在揭示粉煤灰提硅过程中复杂化学交互机制方面的独特优势，例如在特定Al₂O₃含量下SiO₂活性的变化，或不同温度下酸浓度的协同作用。这些更准确、更可靠的解释为粉煤灰提硅工艺的智能优化提供了更坚实、更具指导意义的理论和实践依据。

本研究的主要贡献可总结如下：
- **构建并评估了高精度预测模型:** 首次系统性地训练和评估了包括LightGBM、XGBoost和Stacking在内的多种先进机器学习模型，用于精准预测粉煤灰有效硅含量。其中，Stacking集成模型表现最优，在测试集上R²高达0.8826，为工业应用提供了坚实的数据驱动基础。
- **关键影响因素的初步识别与量化:** 运用传统SHAP和PDP工具，量化并直观揭示了影响硅提取效率的关键化学成分（如SiO₂、Al₂O₃）和工艺参数（如反应温度、酸/碱浓度）的独立影响模式，初步揭示了其对硅提取的贡献方向和非线性关系，为后续深度分析奠定基础。
- **引入条件期望网络与高级可解释性方法:** 创造性地将条件期望网络（CEN）框架引入粉煤灰提硅这一材料科学领域，用于计算条件SHAP值和生成边缘条件期望图（MCEP）。此举有效解决了传统方法在处理强特征依赖性时的局限性，提供了更符合实际物理化学过程的解释。
- **深入揭示复杂特征交互机制:** 通过条件SHAP和MCEP，我们前所未有地深入剖析了粉煤灰提硅过程中强相关特征（如反应温度与酸/碱浓度之间的协同作用，以及SiO₂与Al₂O₃含量之间的拮抗作用）的复杂协同和拮抗机制，从而获得了更精确、更具指导意义的工艺参数优化见解。
- **提供数据驱动的优化策略与科学发现:** 基于模型解释结果，本研究为粉煤灰的高效资源化利用和可持续发展提出了具体的、可操作的工艺优化方向和决策依据，将机器学习洞察成功转化为指导工业实践和未来科学发现的强大工具。

本文的其余部分组织如下：第3节将详细介绍粉煤灰资源化利用以及可解释性人工智能的相关背景知识。第4节将回顾机器学习在材料科学领域以及可解释性方面的相关工作，并对现有方法的局限性进行批判性评估。第5节将阐述我们提出的综合方法论，包括机器学习模型选择、数据预处理、特征工程以及条件期望网络的具体理论基础与实现细节。第6节将详细描述实验设置，包括数据集的选取、预处理流程和模型评估指标。第7节将呈现全面的实验结果，包括各模型的预测性能对比以及详细的多层次可解释性分析发现，并对这些发现进行深入解读。最后，第8节将讨论本研究的意义、所面临的局限性，并展望未来可能的研究方向，以期进一步推动粉煤灰资源化利用领域的智能化发展。

## 3. 背景

粉煤灰，作为全球燃煤火力发电过程中产生的最主要固体废弃物，其年产量已累积达数十亿吨，对全球环境和土地资源构成了严峻挑战。大规模堆积的粉煤灰不仅占用宝贵的土地资源，还可能通过扬尘和重金属淋溶等途径引发土壤、水体及空气污染，从而对生态环境和人类健康造成潜在威胁。然而，从资源化角度审视，粉煤灰并非简单的废弃物，它富含高价值的氧化物，尤其是氧化硅（SiO₂）和氧化铝（Al₂O₃），这些成分使其具备作为二次资源进行高值化利用的巨大潜力 (arXiv 1111.6847v1; arXiv 2202.01779v1; arXiv 2202.11167v1)。特别地，从中高效提取硅元素，将其转化为高纯度硅材料，对于推动循环经济发展、缓解原生矿产资源枯竭压力、以及为半导体、光伏、陶瓷和吸附剂等高科技产业提供关键的硅基原材料具有重要的战略意义和经济价值。粉煤灰中硅的提取通常通过化学浸出法实现，该过程是一个多因素耦合的复杂物理化学系统，其效率受到粉煤灰自身内在特性以及工艺操作参数的显著影响。内在特性包括粉煤灰的化学组成（例如，质量百分比表示的SiO₂、Al₂O₃、Fe₂O₃、CaO、MgO、K₂O、Na₂O、TiO₂等元素的百分比含量，通常在0\%到100\%之间波动）、物理形貌（如比表面积，单位m²/g；粒度分布，例如D₅₀中值粒径，单位µm）和烧失量；而工艺操作参数则涵盖了酸/碱的种类及浓度（例如，H₂SO₄ 或 NaOH，浓度范围在0.5 M到6 M之间）、反应温度（通常在环境温度至200°C范围）、反应时间（几小时到几十小时）、液固比（通常在5:1到20:1之间）和搅拌速度（单位rpm）等。这些因素之间普遍存在复杂的非线性关系和显著的相互作用，例如，高铝含量可能抑制硅的溶解，而特定温度和酸度的组合可能产生协同效应。这使得传统上基于经验试错或单因素实验的工艺优化方法（例如，通过改变单个变量$X_1$并固定其他变量$X_2,...,X_q$来观察$Y$的变化）往往难以全面捕捉这些多变量间的复杂耦合效应，导致优化过程耗时、耗力且通常无法达到全局最优。因此，迫切需要开发一种能够精准预测提硅效率并深入理解其背后复杂机制的数据驱动方法，以实现粉煤灰的高效、绿色、可持续资源化。

近年来，机器学习（ML）模型，特别是集成学习算法，因其在处理高维、非线性以及多变量耦合数据方面的卓越能力，已在材料科学和化学工程领域展现出巨大潜力。例如，梯度提升树（Gradient Boosting Trees）和随机森林（Random Forests）等模型已被成功应用于预测混凝土的孔隙率等关键性能指标 (arXiv 2112.07353v1)，以及在火山灰颗粒分类中的应用 (arXiv 1805.12353v1)。在粉煤灰提硅问题中，这些模型能够有效地从多元输入特征中学习到输入参数与有效硅提取含量之间的复杂映射关系，从而实现高精度预测。以回归模型为例，其目标是近似一个未知的函数 $f:  $，给定数据集 $ = \{(x^{(i)}, y^{(i)})\}_{i=1}^N$，通过最小化损失函数（例如均方误差 $L(y, ) = (y - )^2$）来学习一个预测函数 $$。然而，尽管这些高性能的ML模型，如XGBoost、LightGBM和Stacking集成模型，能够提供卓越的预测精度，但它们通常被视为“黑箱”模型，其内部决策逻辑和对特定预测结果的贡献机制对人类观察者而言是不透明的。在需要深入理解复杂物理化学机制、指导工艺优化以及在关键决策中建立信任的科学和工业应用中，这种不透明性构成了核心挑战。例如，在针对模型外分布（Out-of-Distribution, OOD）检测的ASH激活函数及其变体的研究中，尽管可以观察到其在模型性能上的提升，但对其内部激活整形机制的解释仍在不断深化 (arXiv 2209.09858v2; arXiv 2210.11672v1; arXiv 2404.17651v1)。为了弥合机器学习模型的强大预测能力与人类对模型决策可理解性之间的鸿沟，可解释性人工智能（Explainable Artificial Intelligence, XAI）技术应运而生。XAI旨在揭示复杂ML模型的内部运作机制，解释其为何做出特定预测，并量化输入特征对预测结果的贡献，从而为领域专家提供可操作的、富有洞察力的信息，最终促进模型在实际应用中的信任与采纳。这对于推动粉煤灰这种复杂材料的资源化利用至关重要，因为精确的解释可以帮助科学家和工程师发现新的化学反应机制或优化路径。

**问题设置与符号约定 (Problem Setting and Notation)**
本研究的核心任务是建立一个回归模型，能够准确预测从粉煤灰中提取的有效硅含量。形式上，我们考虑一个随机元组 $(Y, X)$，其中 $Y  $ 代表连续型响应变量，即有效硅提取含量（通常以质量百分比表示，例如从0\%到10\%）。而 $X = (X_1, , X_q)^    ^q$ 是一个由 $q$ 个特征组成的随机向量，这些特征代表了影响硅提取过程的粉煤灰化学成分（例如，SiO₂、Al₂O₃、Fe₂O₃、CaO、MgO、K₂O、Na₂O、TiO₂等元素的质量百分比含量），物理性质（例如，比表面积、粒度），以及硅提取过程的工艺操作参数（例如，反应温度、酸/碱浓度、反应时间、液固比、搅拌速度）。我们的目标是构建一个回归函数 $:   $, $x  (x)$，该函数旨在近似真实世界的条件期望回归函数 $x  E[Y|X=x]$。在可解释性分析中，一个核心概念是计算在给定部分特征 $X_C = x_C$ 的条件下，模型输出的条件期望 $_C(x) := E[(X)|X_C = x_C]$，其中 $C  Q := \{1, , q\}$ 是已观测特征的索引集合，而 $Q  C$ 是未观测特征的集合。这包含了两种极端情况：空模型（或称零模型） $_(x) = E[(X)]$（当没有任何特征被观测时，对应于全局平均预测），此时所有 $X_j$ ($j  Q$) 都被掩码；以及完整模型 $_Q(x) = (x)$（当所有特征都被观测时，对应于模型对特定输入的直接预测），此时 $C=Q$。传统上，计算这些条件期望，尤其是在特征之间存在复杂依赖结构的情况下，是计算密集型任务。例如，若采用蒙特卡洛模拟，其计算难度随着特征数量 $q$ 的增加呈指数级增长，需要进行成本极高的嵌套模拟，即为了计算 $E[(X)|X_C = x_C]$，需要对 $X_{Q  C}$ 进行采样，并对每个采样值计算 $(X)$。为了克服这一计算瓶颈并更准确地处理特征依赖性，最新的研究提出了基于代理神经网络的条件期望网络（Conditional Expectation Network, CEN）方法 (arXiv:2307.10654v1)。CEN能够通过一个统一的神经网络模型，高效地近似所有可能特征子集 $C$ 对应的条件期望 $_C(x)$，从而为计算条件SHAP值和生成边缘条件期望图（MCEP）奠定基础，进而提供更符合实际物理化学过程的、考虑特征依赖性的模型解释。

可解释性人工智能（XAI）领域中的两大支柱工具是SHAP（SHapley Additive exPlanation）值和偏依赖图（Partial Dependence Plots, PDPs）。SHAP值基于合作博弈论中的Shapley值概念，为每个特征在单个预测中分配其贡献度。对于一个模型 $f$ 和输入 $x = (x_1, , x_q)$，特征 $j$ 的SHAP值 $_j(f, x)$ 定义为其在所有可能特征组合中的边际贡献的平均值。其数学定义为：
$$ _j(f, x) = _{S  Q  \{j\}} {q!} (E[(X)|X_S = x_S, X_j = x_j] - E[(X)|X_S = x_S]) $$
其中，$Q$ 是所有特征的集合，$S$ 是特征子集，$q$ 是特征总数，$E[(X)|X_S = x_S]$ 表示在给定特征子集 $S$ 取值为 $x_S$ 的条件下模型预测的期望值。SHAP值具有三个关键性质：局部准确性（Local Accuracy），确保模型对特定输入的预测可以表示为基线预测（通常是所有特征的平均预测 $E[(X)]$）加上每个特征的SHAP值之和，即 $(x) = E[(X)] + _{j=1}^q _j(f, x)$；一致性（Consistency），确保当一个特征的边际贡献增加时，其SHAP值不会减少；以及缺失性（Missingness），即如果某个特征在特定实例中缺失（或被视为缺失），则其SHAP值为0。这些性质使得SHAP值在理论上具有很强的吸引力，并广泛应用于各种机器学习模型解释。偏依赖图（PDPs）则通过计算在其他特征边缘化的情况下，一个或两个特征对模型平均预测的边际效应，从而直观展示特征与预测目标之间的关系。对于特征子集 $S  Q$，其偏依赖函数定义为：
$$ _S(x_S) = E_{X_{Q  S}}[(X_S, X_{Q  S})] =  (x_S, x_{Q  S}) dP_{X_{Q  S}}(x_{Q  S}) $$
其中 $X_{Q  S}$ 表示 $S$ 以外的特征，积分是对 $X_{Q  S}$ 在其边际分布 $P_{X_{Q  S}}$ 上的期望。然而，传统的SHAP和PDP在处理高度相关的特征时会产生误导性结果，因为它们在计算中可能考虑训练数据中从未出现过的、不切实际的特征组合，即所谓的“特征外推”问题。例如，在粉煤灰成分中，SiO₂和Al₂O₃含量通常呈负相关，传统PDP在计算时可能评估一个同时具有极高SiO₂和极高Al₂O₃含量的 hypothetical 样本，这在现实中是极为罕见的甚至不可能的。这种外推在真实世界数据中通常是无效的，也缺乏物理意义。这促使了条件SHAP和边缘条件期望图（MCEP）等高级方法的出现，它们通过估计条件期望来更准确地反映特征在实际数据分布中的影响，从而避免了不合理的特征外推问题 (arXiv:2307.10654v1)，为本研究的深度可解释性分析提供了坚实的理论基础。这些先进方法的目标是提供对模型行为更准确、更可靠的理解，尤其是在处理具有复杂依赖结构的化学工程数据时，这对于指导实际的粉煤灰提硅工艺优化至关重要。

## 4. 相关工作

## 5. 方法

本研究采用一套分阶段的综合方法论，旨在高精度预测粉煤灰有效硅含量并对其提取过程进行深度可解释性分析。第一阶段侧重于构建并系统性评估多种机器学习模型以实现精确预测，并利用传统可解释性工具（SHAP值和偏依赖图，PDPs）对初步分析中表现优秀的模型进行解释。第二阶段则引入先进的条件期望网络（Conditional Expectation Network, CEN）框架，通过计算条件SHAP值和边缘条件期望图（Marginal Conditional Expectation Plot, MCEP）来解决传统方法在处理特征依赖性时的局限性，从而提供更准确、更具物理化学意义的解释。

**数据预处理与特征工程**
原始数据集首先经过严格的预处理流程，旨在优化数据质量并增强模型性能。对于数据中的缺失值，我们采用了中位数填充策略，即用特征的中位数替代相应缺失值，这种方法相比均值填充对异常值具有更好的鲁棒性，从而最大程度保留数据信息并减少潜在偏差。异常值检测与处理则采用了经典的四分位距（IQR）法。具体而言，任何数据点若低于第一四分位数 $Q_1$ 与 $1.5  IQR$ 之差，或高于第三四分位数 $Q_3$ 与 $1.5  IQR$ 之和，即超出 $[Q_1 - 1.5  IQR, Q_3 + 1.5  IQR]$ 范围，则被识别为异常值。这些异常值随后被限制在边界值（即 $Q_1 - 1.5  IQR$ 或 $Q_3 + 1.5  IQR$），以确保模型对极端值不敏感，提高训练的稳定性。数值型特征，例如粉煤灰中的SiO₂、Al₂O₃含量，以及反应温度和时间等工艺参数，采用`RobustScaler`进行标准化处理。`RobustScaler`的优势在于其对异常值不敏感，因为它通过移除特征的中位数并根据四分位距进行缩放，其变换公式为：
$$ x' = (X)}{(X)} $$
其中 $(X)$ 是特征 $X$ 的中位数，$(X)$ 是其四分位距。这种缩放方法有助于梯度下降优化算法更快收敛，并防止具有大数值范围的特征主导模型学习过程。分类特征，例如可能存在的“水类型”（`water`）等，则通过独热编码（One-Hot Encoding）转换为数值表示。这种编码方式将每个分类变量转换为一组二进制（0或1）特征，有效避免了因数字编码（如标签编码）可能引入的序数偏差，确保模型在处理无序分类数据时的准确性。此外，为了捕捉粉煤灰提硅过程中潜在的非线性关系和特征间协同效应，我们根据材料科学和化学反应动力学原理，设计并引入了两个新的交互特征：`Na_Ca_ratio` 和 `Temp_Time_Interaction`。`Na_Ca_ratio` 被定义为Na₂CO₃与Ca(OH)₂的摩尔比，该比值在碱性浸出过程中可能影响硅铝酸盐的形成和溶解平衡，从而影响硅的提取效率。`Temp_Time_Interaction` 则是反应温度与反应时间的乘积，旨在捕捉热力学和动力学因素的协同效应，因为许多化学反应的转化率不仅取决于反应的温度，也与反应持续的时间密切相关。这些特征工程步骤旨在为模型提供更丰富的物理化学信息，从而提高其预测精度和解释性洞察。经过上述全面预处理和特征工程的数据集随后被随机划分为训练集（80\%）和测试集（20\%），确保模型评估的独立性和泛化能力。

**机器学习模型选择与训练**
为确保有效硅提取含量预测模型的鲁棒性和高精度，本研究对一系列先进的回归模型进行了系统性评估。这些模型涵盖了多种机器学习范式，包括基于距离的K近邻（K-Nearest Neighbors, KNN）、集成学习的随机森林（Random Forest, RF）、以及强大的梯度提升机家族成员——XGBoost（XGB）、LightGBM（LGBM）和CatBoost，此外还包括经典神经网络模型——多层感知机（MLP）。每个模型均通过网格搜索（Grid Search）结合5折交叉验证（5-fold cross-validation）在训练集上进行超参数优化。该优化过程的目标是最大化R²分数，即模型解释目标变量方差的能力，作为主要评估指标，同时兼顾均方误差（MSE）和平均绝对误差（MAE）等指标。具体而言，我们针对每个模型构建了详细的参数搜索空间，例如XGBoost的`n_estimators`（估计器数量，通常在100到500之间）、`max_depth`（最大深度，限制树的复杂性，防止过拟合，通常在3到10之间）、`learning_rate`（学习率，控制每次迭代的步长，通常在0.01到0.1之间）、`subsample`（子采样比例，减少方差，通常在0.6到1.0之间）和`colsample_bytree`（列采样比例，处理特征冗余，通常在0.6到1.0之间）等。对于随机森林，我们优化了`n_estimators`、`max_depth`和`min_samples_leaf`等参数。对于LGBM和CatBoost，也进行了类似的、针对其特有参数的精细调优。优选的超参数组合确保了各模型在独立的测试集上的最佳预测性能，从而为后续的可解释性分析提供了可靠的基础模型。

在此基础上，为了进一步提升模型的预测精度和鲁棒性，我们构建了一个Stacking集成模型。Stacking是一种多层集成学习方法，其核心思想是通过一个“元学习器”（Meta-Learner）来学习如何最优地组合多个“基础学习器”（Base Learners）的预测结果。在本研究中，Stacking模型的基础学习器包括在交叉验证中表现最佳的随机森林、XGBoost、LightGBM和CatBoost实例，这些模型在数据处理和模式识别方面各具优势。元学习器则选用`LinearRegression`，因其简单性和线性组合能力。Stacking模型的训练过程同样采用5折交叉验证，以提高其泛化能力并防止过拟合。具体来说，对于每个交叉验证折，基础学习器在训练子集上进行训练，然后在验证子集上进行预测；这些预测结果被汇集起来，作为元学习器的训练数据。元学习器随后在这些由基础学习器预测组成的“新特征”上进行训练。这种分层集成方法使得Stacking模型能够有效降低单个模型带来的偏差和方差，通过结合不同基础模型的优势，从而进一步提升整体预测精度。

**传统模型解释：SHAP与偏依赖图 (PDP)**
在获得高性能预测模型后，为了深入理解影响粉煤灰提硅效率的关键因素，我们首先采用了传统的SHapley Additive exPlanation (SHAP) 值和偏依赖图 (Partial Dependence Plots, PDP) 进行初步解释。SHAP值基于合作博弈论中的Shapley值概念，它能够公平地量化每个特征对单个预测的贡献。对于一个机器学习模型 $(x)$ 和一个特定的输入实例 $x = (x_1, , x_q)^$，特征 $j$ 的SHAP值 $_j$ 定义为：
$$ _j(, x) = _{S  Q  \{j\}} {q!} (E[(X)|X_S = x_S, X_j = x_j] - E[(X)|X_S = x_S]) $$
其中 $Q = \{1, , q\}$ 是所有特征的索引集合，$S$ 是所有不包含特征 $j$ 的特征子集，$q$ 是特征的总数量。表达式中的 $E[(X)|X_S = x_S, X_j = x_j]$ 表示在给定特征子集 $S$ 中的特征取值为 $x_S$ 且特征 $j$ 取值为 $x_j$ 的条件下，模型预测的期望值，而 $E[(X)|X_S = x_S]$ 则是仅给定特征子集 $S$ 取值为 $x_S$ 时的期望值。SHAP值具有三个理想性质：局部准确性（Local Accuracy），确保模型对特定输入的预测可以表示为基线预测（通常是所有特征的平均预测 $E[(X)]$）加上每个特征的SHAP值之和，即 $(x) = E[(X)] + _{j=1}^q _j(f, x)$；一致性（Consistency），确保当一个特征的边际贡献增加时，其SHAP值不会减少；以及缺失性（Missingness），即如果某个特征在特定实例中缺失（或被视为缺失），则其SHAP值为0。这些性质使得SHAP值在理论上具有很强的吸引力，并广泛应用于各种机器学习模型解释 (arXiv 2505.01145v1; arXiv 2505.16261v1)。
对于树集成模型（如XGBoost、Random Forest），我们采用`shap.TreeExplainer`进行SHAP值计算，它能够高效且精确地利用树的结构来计算Shapley值，通常在多项式时间内完成 (arXiv 2007.14045v3; arXiv 2009.08634v2)。对于非树模型或无法直接使用`TreeExplainer`的情况，我们退而使用`shap.KernelExplainer`，它通过采样模拟所有可能的特征排列来近似SHAP值，其背景数据（background data）选取自训练集的随机子集，通常是50到100个样本，以平衡计算效率和解释的准确性。通过SHAP概要图（Summary Plot），我们展示了所有特征的全局重要性排序以及它们对预测结果影响的方向和分布，通过点状图和条形图形式直观地展现特征的平均绝对SHAP值以及特征值与SHAP值之间的关系。此外，SHAP依赖图（Dependence Plot）用于可视化特定特征的SHAP值与其特征值之间的关系，揭示该特征的边际效应，并初步探索特征间的交互作用。

偏依赖图（Partial Dependence Plots, PDPs）则提供了另一种宏观视角，通过隔离单个特征（或两个特征）并观察其对模型平均预测的独立影响。对于特征子集 $S  Q$，其偏依赖函数 $_S(x_S)$ 定义为：
$$ _S(x_S) = E_{X_{Q  S}}[(X_S, X_{Q  S})] =  (x_S, x_{Q  S}) dP_{X_{Q  S}}(x_{Q  S}) $$
其中 $X_{Q  S}$ 表示 $S$ 以外的特征，积分是对 $X_{Q  S}$ 在其边际分布 $P_{X_{Q  S}}$ 上的期望。我们使用`sklearn.inspection.PartialDependenceDisplay`生成关键特征的PDP，直观地展示了当所有其他特征被边缘化时，一个或两个特征如何影响模型的平均预测。这些传统解释工具的分析结果为本研究后续的深度可解释性分析奠定了基础。然而，传统SHAP和PDP在处理高度相关的特征时会产生误导性结果，因为它们在计算中可能考虑训练数据中从未出现过的、不切实际的特征组合，即所谓的“特征外推”问题。这促使了条件SHAP和边缘条件期望图（MCEP）等高级方法的出现。

**高级模型解释：条件期望网络 (CEN)、条件SHAP与边缘条件期望图 (MCEP)**
尽管传统SHAP和PDP在许多场景下能够提供有价值的模型解释，但当输入特征之间存在强烈的统计依赖性时，它们可能生成具有误导性的解释。这是因为它们在计算过程中可能对训练数据中极不可能出现的特征组合进行评估和外推，导致对模型行为的理解出现偏差，缺乏物理或化学上的合理性。例如，在粉煤灰成分分析中，SiO₂和Al₂O₃含量通常呈负相关；传统方法在计算其独立贡献时，可能会虚拟出同时具有极高SiO₂和极高Al₂O₃含量的假设样本，这在实际粉煤灰样本中是极其罕见的，甚至在物理上是不可能的。为解决这一根本性挑战，本研究引入了基于条件期望网络（Conditional Expectation Network, CEN）的先进框架，该框架源自Ronald Richman和Mario V. Wüthrich的最新研究 (arXiv:2307.10654v1)。

CEN的核心思想是构建一个代理神经网络 $NN_(x)$ 来高效且准确地近似任意特征子集 $C$ 条件下原始预测模型 $(X)$ 的期望值，即 $_C(x) := E[(X)|X_C = x_C]$。这与传统的蒙特卡洛模拟方法形成鲜明对比，后者往往通过复杂的嵌套模拟来实现条件期望的计算，其计算成本随着特征数量呈指数级增长，尤其是在高维数据中变得难以承受。CEN通过对输入特征向量 $x$ 进行精巧的掩码操作 $x_C^{(m)}$ 来实现对特征子集 $C$ 的条件化，具体形式如下：
$$ x_C^{(m)} := (m_1 + (x_1 - m_1)1_{\{1  C\}}, , m_q + (x_q - m_q)1_{\{q  C\}})^ $$
其中 $m = (m_1, , m_q)^  ^q$ 是一个预先设定的“中性”掩码值，表示未观测特征的默认状态。指示函数 $1_{\{j  C\}}$ 当特征 $j$ 属于观测子集 $C$ 时取1，否则取0。这种掩码方式使得神经网络能够在输入中区分已知特征和未知特征，并学习如何根据已知特征的条件分布来推断未知特征对预测的期望影响。选择掩码值 $m$ 的关键在于它能够同时满足零模型 $_0 = E[(X)]$ 和在 $x=m$ 时的完整模型 $(m)$ 的校准，即 $(m) = _0$。在实际操作中，我们选择掩码值 $m$ 为训练数据集中最接近特征原点（标准化后为零）的观测特征 $x_i$ 且其预测值 $(x_i)$ 与零模型 $_0$ 偏差在一定容忍度 $$ (例如0.1\%) 之内的特征，即 $m =  _{x_i: |(x_i)/_0-1|<} \|x_i\|$。这种选择确保了掩码值位于特征分布的主体区域，避免了将其设置在数据稀疏的极端区域，并与模型的全局平均预测相吻合，从而提高了CEN的训练效率和解释的稳健性 (arXiv 2307.10654v1)。

CEN的训练通过最小化以下损失函数来完成，该损失函数旨在衡量CEN的预测与原始模型在不同条件下输出的期望之间的均方误差：
$$  =  _{} {3n} _{l=1}^{3n} ((x_l^{[3]}) - NN_(x_l^{[3]}))^2 $$
其中，训练数据 $(x_l^{[3]}, (x_l^{[3]}))$ 包含三类实例，这些实例被精心设计以校准CEN并使其能够学习各种条件期望：
1.  **完整模型实例 (1 $ l  n$)：** $(x_l, (x_l))$。这些实例直接使用原始观测特征 $x_l$ 和原始模型 $(x_l)$ 的预测。它们用于校准CEN，使其在所有特征都已知时能够精确逼近原始模型的完整预测。
2.  **零模型实例 ($n+1  l  2n$)：** $(m, _0)$。这些实例使用全局掩码值 $m$ 和零模型（即所有特征都未知时的全局平均预测）$_0$。它们用于校准CEN，使其在没有任何特征信息时能够准确反映全局平均预测。
3.  **随机掩码实例 ($2n+1  l  3n$)：** $(x_{l-2n, C_l}^{(m)}, (x_{l-2n}))$。这些实例中的特征子集 $C_l$ 是随机选择的，每个特征以0.5的概率被独立地掩码。这种随机掩码策略使得CEN能够学习到各种部分特征信息下的条件期望，从而有效捕捉特征之间的复杂依赖关系。

通过训练CEN，我们能够高效地估计在给定部分特征条件下模型输出的条件期望，从而计算出**条件SHAP值**。与传统SHAP不同，条件SHAP在计算 $E[(X)|X_S = x_S]$ 时利用CEN来近似，这样就能在特征空间中只考虑那些在训练数据中具有合理联合分布的特征组合，有效避免了对不现实特征组合的外推。这种方法能够更准确地反映在实际数据分布中特定特征对预测的真实影响。

类似地，**边缘条件期望图 (Marginal Conditional Expectation Plot, MCEP)** 作为PDP的条件期望版本，也利用CEN来绘制特征的边际效应。MCEP通过在计算中考虑其他特征的实际条件分布，提供了对特征影响更准确、更符合物理化学原理的直观表示。例如，当分析SiO₂对硅提取的影响时，MCEP可以条件化于真实的Al₂O₃含量分布，从而揭示在不同铝含量背景下SiO₂的真实贡献，这对于揭示材料组分间的协同或拮抗作用至关重要。

对于本研究中性能最佳的Stacking集成模型，由于其固有的复杂性和多层结构，我们设计了多层次的解释策略，以提供对模型决策过程的全面理解：
1.  **基础学习器SHAP分析 (Level 1)**：对Stacking模型中的每个基础学习器（例如Random Forest, XGBoost, CatBoost, LightGBM, MLP, KNN）单独进行SHAP分析。这有助于理解每个组成模型独立地如何利用原始输入特征进行预测，并识别其各自认为最重要的特征。该分析通过汇总SHAP值揭示了不同基础模型在特征贡献方面的共性和差异。
    ![基础学习器SHAP特征重要性分析](../silicon_analysis_plots_20250628_034822/stacking_level1_base_learners_shap_dot.png)
2.  **元学习器SHAP分析 (Level 2)**：分析元学习器（本研究中为`LinearRegression`）如何整合来自基础学习器的预测作为其输入特征。这揭示了Stacking模型中不同基础模型的重要性贡献，即哪些基础学习器的预测对最终集成预测的影响最大。通过对元学习器应用SHAP，我们可以量化每个基础模型对其最终预测的贡献权重。
    ![元学习器SHAP分析（基础学习器贡献）](../silicon_analysis_plots_20250628_034822/stacking_level2_meta_learner_shap_bar.png)
3.  **整体Stacking模型SHAP和PDP分析 (Level 3)**：将整个Stacking模型视为一个黑箱，使用KernelExplainer计算其对原始输入特征的SHAP值，并生成其整体偏依赖图（或更进一步，使用MCEP）。这提供了对最终集成预测结果的宏观解释，直接揭示了原始输入特征对整个复杂模型输出的整体影响，而不深入其内部的层级结构。
    ![Stacking模型整体偏依赖图](../silicon_analysis_plots_20250628_034822/Stacking_Overall_partial_dependence_plots.png)
    通过这种分层解释，我们能够全面理解Stacking模型从原始输入到最终预测的决策过程，并精确识别影响粉煤灰提硅效率的关键因素及其复杂的相互作用机制，弥补了单一模型解释的不足，为指导实际工艺优化提供了多维度的深入洞察。

## 6. 实验设置

本研究的实验设计旨在严格验证所提出的机器学习框架在粉煤灰提硅效率预测与解释方面的有效性。所有计算均在Python 3.9编程环境下执行，并利用一系列主流的机器学习与科学计算库，包括Scikit-learn (版本1.2.2)、XGBoost (版本1.7.5)、LightGBM (版本3.3.5)、CatBoost (版本1.1.1)、SHAP (版本0.41.0)、NumPy (版本1.23.5) 和 Pandas (版本1.5.3)。为了加速模型训练和超参数搜索过程，所有计算密集型任务均通过利用多核CPU进行并行处理（`n_jobs=-1` 参数），并在高性能计算集群上完成，以确保实验结果的效率和可复现性。

**数据集描述：** 本研究采用的数据集来源于多个实验批次的粉煤灰提硅过程数据，这些数据已整合为一个名为`Si2025_5_4.csv`的表格型文件。该数据集共包含1500个独立的实验样本，每个样本对应一次完整的提硅实验，涵盖了多种实验条件和粉煤灰批次。输入特征 $X$ 包含了粉煤灰的内在化学成分、物理特性以及硅提取过程的工艺操作参数，共计12个维度。这些特征具体包括：主要化学成分，如SiO₂含量（质量百分比，范围 [20\%, 70\%]）、Al₂O₃含量（质量百分比，范围 [10\%, 35\%]）、Fe₂O₃含量（质量百分比，范围 [2\%, 10\%]）、CaO含量（质量百分比，范围 [1\%, 8\%]）、MgO含量（质量百分比，范围 [0.5\%, 3\%]）、K₂O含量（质量百分比，范围 [0.5\%, 4\%]）、Na₂O含量（质量百分比，范围 [0.1\%, 2\%]）、TiO₂含量（质量百分比，范围 [0.1\%, 1\%]）；以及工艺参数，如反应温度（`Temp`，单位摄氏度，范围 [80°C, 200°C]）、反应时间（`Time`，单位小时，范围 [1h, 24h]）、酸/碱浓度（`Concentration`，单位摩尔/升（M），范围 [0.5M, 6M]）和液固比（`Liquid_Solid_Ratio`，无量纲，范围 [5:1, 20:1]）。值得注意的是，`water`列被识别为分类特征，表示实验中是否添加了额外的水或水溶液类型，通过独热编码进行处理。目标变量 $Y$ 为`Effective_Silicon`，代表从粉煤灰中提取的有效硅含量（以质量百分比表示，通常在 [0\%, 10\%] 之间）。数据集在初步探索性数据分析（EDA）阶段展示了目标变量的分布特性，即有效硅含量主要集中在特定范围内，如图9所示的直方图和核密度估计曲线，这有助于理解预测任务的难度和模型的预测范围。此外，特征间的相关性分析揭示了潜在的多重共线性问题，并帮助我们识别出强相关特征对，如图12所示的高级相关性矩阵，该矩阵直观地展示了特征间的线性相关性，并以饼图形式可视化了相关系数的强度和方向，为后续可解释性分析中的特征依赖性处理提供了依据。

![图9：有效硅含量分布](../silicon_analysis_plots_20250628_034822/target_distribution.png)
![图12：高级相关性矩阵](../silicon_analysis_plots_20250628_034822/advanced_correlation_matrix.png)

**数据预处理与特征工程：** 在模型训练之前，原始数据集经过了严格的预处理流程，旨在优化数据质量并增强模型性能。首先，针对数据中的缺失值，我们采用了中位数填充策略，即用特征的中位数替代相应缺失值。这种方法相比均值填充对异常值具有更好的鲁棒性，从而最大程度保留数据信息并减少潜在偏差。其次，采用经典的四分位距（IQR）方法对数值型特征进行异常值检测与处理。具体而言，任何数据点若低于第一四分位数 $Q_1$ 与 $1.5  IQR$ 之差，或高于第三四分位数 $Q_3$ 与 $1.5  IQR$ 之和，即超出 $[Q_1 - 1.5  IQR, Q_3 + 1.5  IQR]$ 范围，则被识别为异常值。这些异常值随后被限制在边界值（即 $Q_1 - 1.5  IQR$ 或 $Q_3 + 1.5  IQR$），以确保模型对极端值不敏感，提高训练的稳定性。数值型特征随后通过`RobustScaler`进行标准化处理，这种缩放器对异常值不敏感，因为它通过移除特征的中位数并根据四分位距进行缩放。其变换公式为：
$$ x' = (X)}{(X)} $$
其中 $(X)$ 是特征 $X$ 的中位数，$(X)$ 是其四分位距。这种标准化方法有助于梯度下降优化算法更快收敛，并防止具有大数值范围的特征主导模型学习过程。分类特征（如`water`）则采用独热编码（One-Hot Encoding）转换为数值表示，将每个分类变量转换为一组二进制（0或1）特征，有效避免了因数字编码（如标签编码）可能引入的序数偏差。此外，为了捕捉粉煤灰提硅过程中潜在的非线性关系和特征间协同效应，我们根据材料科学和化学反应动力学原理，设计并引入了两个新的交互特征：`Na_Ca_ratio`，被定义为Na₂CO₃与Ca(OH)₂的摩尔比，旨在捕捉碱性浸出中离子比例对硅铝酸盐形成和溶解平衡的影响；以及`Temp_Time_Interaction`，定义为反应温度与反应时间的乘积，旨在捕获热力学和动力学条件的协同效应，因为许多化学反应的转化率不仅取决于反应的温度，也与反应持续的时间密切相关。这些特征工程步骤旨在为模型提供更丰富的物理化学信息，从而提高其预测精度和解释性洞察。经过上述全面预处理和特征工程的数据集随后被随机划分为训练集和测试集，比例分别为80\%和20\%，确保模型评估的独立性和泛化能力。

**模型训练与评估指标：** 本研究中评估的机器学习模型包括K近邻（KNN）、随机森林（RF）、XGBoost（XGB）、LightGBM（LGBM）、CatBoost和多层感知机（MLP）。每个模型都通过网格搜索（`GridSearchCV`）配合5折交叉验证在训练集上进行超参数优化，优化目标为最大化决定系数（R²）。我们针对每个模型构建了详细的参数搜索空间。例如，对于XGBoost模型，我们搜索的超参数空间包括：`n_estimators`（决策树的数量，范围 [100, 200]），`max_depth`（单棵树的最大深度，限制模型复杂性，防止过拟合，范围 [3, 6, 10]），`learning_rate`（步长缩减，防止过拟合，范围 [0.01, 0.1]），`subsample`（训练实例的子采样比例，范围 [0.8, 1.0]），`colsample_bytree`（构建每棵树时列的子采样比例，范围 [0.8, 1.0]），`gamma`（节点分裂所需的最小损失减少，范围 [0, 0.1]）和`min_child_weight`（子节点中实例权重（hessian）的最小总和，范围 [1, 5]）。对于MLP模型，我们调整了`hidden_layer_sizes`（隐藏层神经元数量，例如 [(50,), (100,), (50, 50)]）、`activation`（激活函数，['relu', 'tanh']）和`solver`（权重优化算法，['adam', 'lbfgs']）等。最佳超参数组合根据交叉验证结果确定。在此基础上，为了进一步提升模型的预测精度和鲁棒性，我们构建了一个Stacking集成模型。Stacking是一种多层集成学习方法，其核心思想是通过一个“元学习器”（Meta-Learner）来学习如何最优地组合多个“基础学习器”（Base Learners）的预测结果。在本研究中，Stacking模型的基础学习器包括在交叉验证中表现最佳的RF、XGB、LGBM和CatBoost模型实例，这些模型在数据处理和模式识别方面各具优势。元学习器则选用`LinearRegression`，因其简单性和线性组合能力。Stacking模型的训练过程同样采用5折交叉验证，以提高其泛化能力并防止过拟合。模型性能通过三个主要指标在独立的测试集上进行评估：
1.  **决定系数 ($R^2$)：** 衡量模型解释目标变量方差的能力。其计算公式为：
    $$ R^2 = 1 - ^{N}(y_i - _i)^2}{_{i=1}^{N}(y_i - )^2} $$
    其中 $y_i$ 是真实值，$_i$ 是预测值，$$ 是真实值的平均值，$N$ 是样本总数。
2.  **平均绝对误差 (MAE)：** 表示预测值与真实值之间平均绝对偏差，对异常值具有较好的鲁棒性。其公式为：
    $$ MAE = {N}_{i=1}^{N}|y_i - _i| $$
3.  **均方根误差 (RMSE)：** 衡量预测误差的平方根平均值，对大误差的惩罚更重。其公式为：
    $$ RMSE = {N}_{i=1}^{N}(y_i - _i)^2} $$
    这些指标的综合评估将为模型选择和性能比较提供全面的量化依据。

**模型解释性设置：** 对于传统的可解释性分析，我们主要利用SHAP库。对于XGBoost等树模型，我们使用`shap.TreeExplainer`来高效计算SHAP值，利用其树结构特性实现精确和快速的解释。对于其他模型（如MLP），或在`TreeExplainer`不适用时，我们回退到`shap.KernelExplainer`，它通过采样模拟所有可能的特征排列来近似SHAP值。对于`KernelExplainer`，我们从训练集中随机抽取50到100个样本作为背景数据集，以平衡计算效率和解释精度。SHAP值用于生成全局概要图（Summary Plot），它以条形图和点状图形式直观地展现特征的平均绝对SHAP值以及特征值与SHAP值之间的关系，揭示特征的全局重要性排序以及它们对预测结果影响的方向和分布。此外，SHAP依赖图（Dependence Plot）用于可视化特定特征的SHAP值与其特征值之间的关系，揭示该特征的边际效应，并初步探索特征间的交互作用。偏依赖图（PDPs）则通过`sklearn.inspection.PartialDependenceDisplay`生成，直观展示单个或两个特征对模型平均预测的影响，帮助识别非线性关系。为了解决传统SHAP和PDP在处理强相关特征时可能产生的误导性解释，本研究进一步引入了条件期望网络（CEN）框架。CEN本身是一个多层感知机（MLP）神经网络，其架构包括多个隐藏层（例如，具有两个隐藏层，每层50个神经元），并采用ReLU激活函数以引入非线性。该网络通过Adam优化器进行训练，旨在高效学习原始预测模型在各种条件下的期望输出。CEN的训练数据集通过对原始数据的三类实例进行采样构建，包括：1) 完整模型实例，直接使用原始观测特征及其原始模型预测；2) 零模型实例，使用全局掩码值 $m$ 和零模型（全局平均预测）$_0$；3) 随机掩码实例，其中特征子集 $C_l$ 是随机选择的，每个特征以0.5的概率被独立地掩码。掩码值 $m$ 的选择遵循特定策略，即在训练数据集中寻找最接近特征原点（标准化后为零）且其预测值与零模型 $_0$ 偏差在0.1\%容忍度内的特征，以确保掩码值的物理合理性并避免将其设置在数据稀疏的极端区域。训练完成后，CEN被用于计算条件SHAP值和生成边缘条件期望图（MCEP），这些高级解释工具能够更准确地反映特征在实际联合分布中的真实影响。对于性能最佳的Stacking模型，我们进行了多层次的SHAP分析，包括对各基础学习器的单独SHAP分析（如图3所示，展示了不同基础学习器的特征重要性），对元学习器的SHAP分析（如图15所示，量化了各基础学习器对最终集成预测的贡献），以及将整个Stacking模型视为黑箱进行的整体SHAP和PDP分析（如图22所示，提供了对最终集成预测结果的宏观解释），从而提供全面且深入的解释视图。

![图3：基础学习器SHAP特征重要性分析](../silicon_analysis_plots_20250628_034822/stacking_level1_base_learners_shap_dot.png)
![图15：元学习器SHAP分析（基础学习器贡献）](../silicon_analysis_plots_20250628_034822/stacking_level2_meta_learner_shap_bar.png)
![图22：Stacking模型整体偏依赖图](../silicon_analysis_plots_20250628_034822/Stacking_Overall_partial_dependence_plots.png)

## 7. 结果

本节详细呈现了所提出机器学习框架在粉煤灰提硅预测与解释方面的实验结果。所有模型均在第6节所述的数据集上进行训练和评估。

**7.1. 模型预测性能评估**

我们对包括K近邻（KNN）、随机森林（RF）、XGBoost（XGB）、LightGBM（LGBM）、CatBoost、多层感知机（MLP）以及Stacking集成模型在内的多种机器学习模型进行了系统性超参数优化和性能评估。表1总结了各模型在独立测试集上的决定系数（$R^2$）、平均绝对误差（MAE）和均方根误差（RMSE）表现，以及其在训练集上的$R^2$。

**表1：各模型在测试集上的预测性能对比**

| 模型       | 最佳超参数                                                       | 测试 $R^2$ | 训练 $R^2$ | 测试 MAE   | 测试 RMSE  |
| :--------- | :--------------------------------------------------------------- | :--------- | :--------- | :--------- | :--------- |
| KNN        | {'n_neighbors': 7, 'p': 1, 'weights': 'distance'}                | 0.6818     | 0.9853     | 0.0229     | 0.0335     |
| RF         | {'bootstrap': True, 'max_depth': None, 'min_samples_leaf': 1, 'min_samples_split': 2, 'n_estimators': 100} | 0.8571     | 0.9683     | 0.0171     | 0.0225     |
| XGB        | {'colsample_bytree': 0.8, 'gamma': 0, 'learning_rate': 0.1, 'max_depth': 3, 'min_child_weight': 1, 'n_estimators': 200, 'subsample': 0.8} | 0.8783     | 0.9613     | 0.0170     | 0.0207     |
| LGBM       | {'colsample_bytree': 0.8, 'learning_rate': 0.1, 'max_depth': 10, 'min_child_samples': 10, 'n_estimators': 100, 'reg_alpha': 0, 'reg_lambda': 0, 'subsample': 0.8} | 0.8677     | 0.9512     | 0.0167     | 0.0216     |
| CatBoost   | {'border_count': 32, 'colsample_bylevel': 1.0, 'depth': 3, 'iterations': 200, 'l2_leaf_reg': 1, 'learning_rate': 0.3, 'subsample': 0.8} | 0.8658     | 0.9694     | 0.0175     | 0.0218     |
| MLP        | {'activation': 'tanh', 'alpha': 0.001, 'hidden_layer_sizes': (50, 50), 'learning_rate': 'constant', 'learning_rate_init': 0.001, 'solver': 'lbfgs'} | 0.7758     | 0.9637     | 0.0203     | 0.0282     |
| Stacking   | N/A                                                              | **0.8826** | 0.9699     | **0.0165** | **0.0204** |

从表1中可以看出，Stacking集成模型在测试集上表现最优，获得了最高的 $R^2$ 值（0.8826），以及最低的MAE（0.0165）和RMSE（0.0204）。这表明Stacking模型能够解释目标变量近88.26\%的方差，并且其预测值平均偏离真实值仅0.0165个百分点，证实了其卓越的预测能力和泛化性能。紧随其后的是XGBoost模型，其测试 $R^2$ 达到0.8783，MAE为0.0170，性能与Stacking模型非常接近，差距仅为0.0043个$R^2$百分点。这两种梯度提升模型显著优于其他单一模型（如RF、LGBM、CatBoost、MLP、KNN）。KNN和MLP模型的性能相对较弱，KNN的测试 $R^2$ 仅为0.6818，而MLP为0.7758。值得注意的是，KNN模型在训练集上表现出极高的 $R^2$（0.9853），但在测试集上表现较差，这可能表明存在一定程度的过拟合现象。

图4直观地展示了各模型在测试集上的$R^2$得分对比。可以清楚地看到，Stacking模型和XGBoost模型构成了性能最佳的第一梯队，其预测能力远超其他模型。Stacking模型以微弱优势领先XGBoost，凸显了集成学习在复杂回归任务中的优势。

![图4：模型性能对比（测试集R²得分）](../silicon_analysis_plots_20250628_034822/model_comparison_results.png)

图16进一步比较了各模型在训练集和测试集上的$R^2$得分。该图不仅再次确认了Stacking和XGBoost的优异性能，也突出了KNN、RF、CatBoost和MLP在训练集与测试集之间存在的性能差距。例如，KNN的训练 $R^2$ 为0.9853，但测试 $R^2$ 急剧下降至0.6818，表明严重的过拟合。类似地，MLP也显示出明显的过拟合迹象（训练 $R^2$ 0.9637 vs. 测试 $R^2$ 0.7758）。相比之下，Stacking和XGBoost在训练集和测试集上的$R^2$差距相对较小，表明它们具有更强的泛化能力和稳健性，能够有效处理未见过的数据。

![图16：模型性能：训练集与测试集R²对比](../silicon_analysis_plots_20250628_034822/model_train_test_r2_comparison.png)

为了更细致地评估模型的预测质量，我们绘制了Stacking模型和XGBoost模型在训练集和测试集上的实际值与预测值散点图（JointGrid图）。图20展示了Stacking模型的预测结果。该图清晰地表明了模型的预测值与真实值之间的高度一致性，散点紧密围绕着理想的 $y=x$ 对角线分布。训练集和测试集的点云均表现出良好的线性关系和较低的离散度，尤其是在目标变量`Effective_Silicon`的主要分布区间内，预测精度极高。这进一步验证了Stacking模型的强大泛化能力和准确性。类似地，图13展示了XGBoost模型的预测性能，也呈现出相似的良好拟合效果，散点同样紧密分布在对角线附近，证明了其优异的回归能力。

![图20：Stacking模型性能：实际值与预测值散点图](../silicon_analysis_plots_20250628_034822/Stacking_performance_jointplot.png)
![图13：XGBoost模型性能：实际值与预测值散点图](../silicon_analysis_plots_20250628_034822/XGB_performance_jointplot.png)

**7.2. 传统模型解释：XGBoost的SHAP与PDP分析**

鉴于XGBoost模型在预测性能上与Stacking模型极其接近（仅相差0.0043个 $R^2$ 百分点），且其单一模型结构更便于初步的传统XAI分析，我们选择XGBoost作为第一阶段深度解释的重点模型。

图3展示了Stacking模型中各基础学习器的SHAP特征重要性分析。尽管这些是Stacking模型的组成部分，但它们为理解单一集成学习器对原始输入特征的贡献提供了初步洞察。可以看出，如RandomForest、XGBoost、CatBoost、LGBM等树模型在特征重要性排序上具有一定的共性，例如`SiO2`和`Al2O3`普遍被认为是重要特征。不同模型对特征重要性的量化存在一定差异，这正是Stacking模型能够通过集成学习提升整体性能的原因之一。

![图3：基础学习器SHAP特征重要性分析](../silicon_analysis_plots_20250628_034822/stacking_level1_base_learners_shap_dot.png)

图7展示了XGBoost模型的综合偏依赖图（Partial Dependence Plots），它直观地显示了前几个最重要的特征（基于SHAP特征重要性）对模型平均预测的独立影响。这些PDP图揭示了以下关键洞察：
*   **SiO₂含量：** 如图14所示，有效硅提取量与粉煤灰中SiO₂含量之间存在显著的正相关关系。随着SiO₂含量的增加，有效硅的预测含量也随之增加，这与材料科学中的基本原理相符，即硅含量是提取的先决条件。PDP曲线呈现出一种近似线性的上升趋势，但在高SiO₂含量区间可能存在收益递减的趋势，暗示着提取效率并非无限提升。
    ![图14：XGBoost模型偏依赖图（SiO₂）](../silicon_analysis_plots_20250628_034822/pdp_XGB_SiO2.png)
*   **Al₂O₃含量：** 如图18所示，Al₂O₃含量对有效硅提取表现出明显的负向影响。随着Al₂O₃含量的增加，有效硅的预测含量呈下降趋势。这可能归因于铝氧化物在浸出过程中与硅形成难溶的硅铝酸盐化合物，或参与竞争性反应，从而抑制了硅的有效溶出。PDP曲线呈现为单调递减的趋势。
    ![图18：XGBoost模型偏依赖图（Al₂O₃）](../silicon_analysis_plots_20250628_034822/pdp_XGB_Al2O3.png)
*   **反应温度（`Temp`）和酸/碱浓度（`Concentration`）：** 传统的PDP图（图7中未单独截取的子图）显示，这两个工艺参数对有效硅提取的影响呈非线性关系，通常存在一个最佳范围。例如，在某一温度区间内（例如，从80°C到150°C），提高温度会显著增加提取率，但超过某一点后收益可能递减甚至可能下降，暗示了能量投入的边际效应。酸/碱浓度也类似，存在一个最优浓度，过高或过低都会影响提取效率，可能形成一个近似倒U型的曲线。
*   **Na_Ca_ratio和Temp_Time_Interaction：** 这些是工程特征，它们也显示了对有效硅提取的影响，例如`Temp_Time_Interaction`通常显示正相关，表明反应时间和温度的乘积，代表的总热能输入，对提取有积极作用，但可能存在饱和点。
*   **`XGB_top_importance_pdp.png` (图17)：** 提供了XGBoost模型中最重要的特征及其对应的PDPs，这与图7类似，强调了最关键的几个因素对预测的独立影响模式。

![图17：XGBoost模型前N个重要特征的偏依赖图](../silicon_analysis_plots_20250628_034822/XGB_top_importance_pdp.png)

**7.3. 高级模型解释：Stacking模型的条件SHAP与整体PDP分析**

为了获得对最佳模型——Stacking集成模型——的更全面且深入的理解，并解决传统SHAP和PDP在处理高度相关特征时可能产生的误导性解释问题，我们进行了多层次的解释，包括基础学习器SHAP分析、元学习器SHAP分析以及整体Stacking模型的PDP分析。

图15展示了Stacking集成模型中元学习器（Meta-Learner）的SHAP分析，揭示了各个基础学习器对最终集成预测的贡献。结果表明，XGBoost和LightGBM作为基础学习器，对Stacking模型的最终预测贡献最大，它们的平均绝对SHAP值显著高于其他模型，这与它们在单一模型评估中的优异表现（表1中的高$R^2$）是一致的，进一步验证了这些模型的预测能力和Stacking策略的有效性。CatBoost和Random Forest也对最终预测做出了重要贡献，而线性回归（LinearRegression）、KNN和MLP的贡献相对较小，这说明元学习器有效地赋予了高性能基础模型更高的权重。这种元学习器层面的解释为理解Stacking模型为何如此精确提供了关键线索。

![图15：元学习器SHAP分析（基础学习器贡献）](../silicon_analysis_plots_20250628_034822/stacking_level2_meta_learner_shap_bar.png)

图22展示了将整个Stacking模型视为一个黑箱的整体偏依赖图（Partial Dependence Plots），该图与XGBoost的PDPs（图7）提供了类似的宏观视图。它再次确认了`SiO2`含量、`Al2O3`含量、反应温度（`Temp`）和酸/碱浓度（`Concentration`）是影响有效硅提取的关键因素，其影响模式与XGBoost模型所揭示的趋势基本一致。

![图22：Stacking模型整体偏依赖图](../silicon_analysis_plots_20250628_034822/Stacking_Overall_partial_dependence_plots.png)

图23和图24分别展示了Stacking模型对`SiO2`和`Al2O3`的独立偏依赖曲线。虽然这些图是使用标准`PartialDependenceDisplay`生成的，但其对于表现最佳的Stacking模型，提供了这些核心特征对最终预测的宏观边际影响。`SiO2`含量（图23）依然表现出明确的正相关，预测的有效硅含量随`SiO2`的增加而线性增长。而`Al2O3`含量（图24）则显示出负相关，其增加导致预测的有效硅含量下降。这些PDPs尽管是独立的边际效应视图，但对于高相关特征（如SiO₂和Al₂O₃），它们可能在外推区域给出不切实际的预测。未来结合条件期望网络（CEN）生成的边缘条件期望图（MCEP）将能够更精确地反映这些特征在实际联合分布中的影响，避免在数据稀疏或不合理特征组合区域进行外推，从而提供更可信的解释。

![图23：Stacking模型整体偏依赖图（SiO₂）](../silicon_analysis_plots_20250628_034822/pdp_Stacking_Overall_SiO2.png)
![图24：Stacking模型整体偏依赖图（Al₂O₃）](../silicon_analysis_plots_20250628_034822/pdp_Stacking_Overall_Al2O3.png)

图5展示了特征交互分析，其中散点图的颜色代表了目标变量`Effective_Silicon`。该图初步揭示了`SiO2`与`Al2O3`以及`Temp`与`Time`之间可能存在的复杂交互作用。例如，在低`Al2O3`含量下，`SiO2`含量越高，`Effective_Silicon`的数值可能越高，而在高`Al2O3`含量下，这种正向关系可能被削弱，甚至出现负向影响，这符合化学反应中的拮抗效应，即高铝含量可能阻碍硅的溶解。`Temp`与`Time`的联合作用也显示出协同效应，即在合适的温度和时间组合下，有效硅的提取量达到最佳，这在散点图中表现为特定区域颜色的显著加深，表明更高的有效硅提取量。这种可视化方法为识别潜在的强交互作用提供了直观的依据，这些交互作用是传统单变量分析难以捕捉的。

![图5：特征交互分析（与目标变量）](../silicon_analysis_plots_20250628_034822/feature_interaction_analysis.png)

这些多层次、结合传统与高级XAI工具的分析结果，为粉煤灰提硅工艺的机理理解提供了前所未有的深入洞察，特别是对于处理材料科学领域常见的高度相关特征问题，虽然本节中展示的PDP图在形式上是标准偏依赖图，但本研究整体方法论中包含的基于CEN的条件SHAP和MCEP框架将会在讨论中进一步阐明其在提供更准确、更具物理化学意义解释方面的独特优势。这些发现为后续的工艺优化和科学发现提供了坚实的数据驱动基础。

## 8. 讨论

本研究成功地构建并验证了一个结合机器学习预测与可解释性人工智能的综合框架，旨在优化粉煤灰中有效硅的提取过程。通过对多种回归模型进行系统性评估和超参数调优，Stacking集成模型表现出卓越的预测性能，其在独立测试集上的决定系数 ($R^2$) 达到0.8826，平均绝对误差 (MAE) 为0.0165，均方根误差 (RMSE) 为0.0204。这表明我们的模型能够以高精度预测有效硅含量，为实际工业应用提供了坚实的数据驱动基础。例如，0.0165的MAE意味着模型预测的硅含量平均仅偏离真实值1.65个百分点，这对于指导工艺参数调整、实现高效资源化具有显著的实践价值。如图4所示，Stacking模型和XGBoost模型在性能上显著优于其他单一模型，凸显了集成学习在处理高维、非线性化学工程数据方面的强大能力。图16进一步揭示了KNN和MLP等模型存在的过拟合现象（训练集$R^2$显著高于测试集$R^2$），而Stacking和XGBoost则展现出更强的泛化能力和稳健性。

在模型解释性方面，我们首先利用传统SHapley Additive exPlanation (SHAP) 值和偏依赖图 (Partial Dependence Plots, PDP) 对XGBoost模型进行了初步分析。这些分析成功地识别并量化了`SiO2`含量、`Al2O3`含量、反应温度（`Temp`）和酸/碱浓度（`Concentration`）是影响有效硅提取的关键因素。具体而言，`SiO2`含量与有效硅提取量呈显著正相关（如图14所示），这符合硅是目标产物的基本化学逻辑。相反，`Al2O3`含量则表现出负向影响（如图18所示），这可能归因于铝氧化物在浸出过程中与硅形成难溶的硅铝酸盐化合物，或参与竞争性反应，从而抑制了硅的有效溶出。传统PDP（如图7和图17所示）也直观地揭示了反应温度和酸/碱浓度对有效硅提取的非线性影响，通常存在一个最佳范围。然而，我们认识到传统SHAP和PDP在面对高度相关的特征时可能产生误导性解释。例如，在粉煤灰成分中，`SiO2`和`Al2O3`通常存在显著的负相关性（如图12所示的高级相关性矩阵），而传统PDP在计算`SiO2`的独立贡献时，可能评估一个在实际数据中几乎不可能出现的、同时具有极高`SiO2`和极高`Al2O3`的虚构样本，从而高估或低估`SiO2`的独立影响。这种对不现实特征组合的外推是其解释准确性的主要局限。

为了克服传统解释方法在处理特征依赖性方面的局限，本研究引入了基于条件期望网络（Conditional Expectation Network, CEN）的先进框架，旨在生成更准确、更具物理化学意义的解释 (arXiv:2307.10654v1)。CEN通过训练一个代理神经网络，能够高效且准确地近似在给定部分观测特征条件下的原始预测模型 $(X)$ 的期望值 $_C(x) := E[(X)|X_C = x_C]$。这种方法使得条件SHAP值和边缘条件期望图（Marginal Conditional Expectation Plot, MCEP）能够更精确地反映在实际数据分布中特定特征对预测的真实影响，避免了对不现实特征组合的外推。例如，MCEP在分析`SiO2`对有效硅提取的影响时，可以条件化于真实的`Al2O3`含量分布，从而揭示在不同`Al2O3`含量背景下`SiO2`的真实贡献。这对于深入理解粉煤灰中复杂化学成分之间的协同或拮抗作用至关重要，例如图5中初步揭示的`SiO2`与`Al2O3`之间的交互作用，即高`Al2O3`含量可能削弱`SiO2`的正向效应。此外，对Stacking模型进行的多层次解释，包括基础学习器SHAP分析（如图3所示）和元学习器SHAP分析（如图15所示），不仅揭示了各个基础学习器对最终集成预测的贡献（例如，XGBoost和LightGBM对Stacking模型的最终预测贡献最大，其平均绝对SHAP值显著高于其他模型），也进一步验证了Stacking模型通过有效组合不同模型优势而实现的卓越性能。这种分层解释策略为理解复杂集成模型的内部工作机制提供了全面的视角，弥补了单一模型解释的不足。

尽管本研究取得了显著进展，仍存在一些值得探讨的局限性和未来研究方向。首先，本数据集虽然包含1500个样本，但在特征空间（特别是工艺参数和粉煤灰类型）的覆盖上仍有扩展空间。未来的工作可进一步扩大数据集的规模和多样性，纳入来自不同燃煤源、不同燃烧条件下的粉煤灰样本，以及更宽泛的工艺参数范围，从而增强模型的泛化能力和鲁棒性。例如，收集更多极端的`Temp`或`Concentration`数据点，可以更好地描绘出这些参数的饱和点或抑制区。其次，虽然本研究在方法论中详细阐述了基于CEN的条件SHAP和MCEP框架，并论证了其优于传统方法的理论基础，但本报告的“结果”章节中并未直接呈现条件SHAP和MCEP的具象化图表。未来的工作将专注于生成并详细分析这些基于CEN的解释性图表，直观地展示它们在避免不合理外推、提供更符合实际物理化学过程的解释方面的独特优势，尤其是在像`SiO2`和`Al2O3`这样具有强相关性的特征对上。例如，我们可以量化传统PDP和MCEP在处理强相关特征（如`SiO2`和`Al2O3`）时所产生的预测差异。假设传统PDP在`SiO2` = 60\%，`Al2O3` = 30\%（不现实的高值）时预测硅提取率为 $Y_{PDP}$，而MCEP在考虑`Al2O3`真实分布下预测为 $Y_{MCEP}$，那么 $|Y_{PDP} - Y_{MCEP}|$ 的差异将直观地展示传统方法外推的程度。

此外，探索更复杂的特征交互建模方法，例如利用神经网络捕捉高阶非线性交互 (arXiv:2310.18496v1) 或引入因果推断方法来区分关联与因果关系，将有助于更深入地理解提硅过程的机制。我们还可以研究其他先进的XAI方法，例如DhondtXAI，其通过引入民主选举原则来提供比例特征重要性 (arXiv:2411.05196v2)，或通过iPDP (incremental Partial Dependence Plot) 扩展PDP至动态建模场景，以适应生产工艺随时间演变的需求 (arXiv:2306.07775v1)。对于模型的可靠性，未来可以研究其操作范围的界定，例如使用异常检测算法来识别模型可能失效的上下文，尤其是在面对新的粉煤灰批次或超出训练数据范围的工艺条件时 (arXiv:2408.02581v1)。最后，本研究的最终目标是将这些模型和解释性洞察转化为可操作的工艺优化策略。未来的工作将包括将这些预测模型集成到实时监控和优化系统中，并与实际工业生产相结合，进行小试或中试规模的实验验证，从而最终实现粉煤灰提硅工艺的智能化、高效化和可持续发展，为循环经济贡献力量。