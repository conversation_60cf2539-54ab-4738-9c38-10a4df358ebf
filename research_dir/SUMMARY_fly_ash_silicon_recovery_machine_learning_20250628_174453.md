Title: Surface modification of fly ash by mechano-chemical treatment
Summary: Fly ash (FA), as an industry by-product has attracted much attention as a
suitable supplier of silicon (Si) and aluminum (Al) in preparation of
geopolymer for the sustainable environment and material science applications.
Here, the effect of mechano-chemical (MC) treatment for surface modification of
FA powder was systemically investigated by analyzing the size, surface
morphology, crystal structure and dissolubility of Si and Al ions in alkali
solutions. The dissolution dynamic as a function of MC treatment time is
discussed in details and concluded with a recombination model of "grinding
effect" and "activation effect", which can be well correlated with the change
of surface morphology and crystal structure, respectively.
Publication Date: 2022-02-03
Categories: cond-mat.mtrl-sci
arXiv paper ID: 2202.01779v1

Title: Hard ASH: Sparsity and the right optimizer make a continual learner
Summary: In class incremental learning, neural networks typically suffer from
catastrophic forgetting. We show that an MLP featuring a sparse activation
function and an adaptive learning rate optimizer can compete with established
regularization techniques in the Split-MNIST task. We highlight the
effectiveness of the Adaptive SwisH (ASH) activation function in this context
and introduce a novel variant, Hard Adaptive SwisH (Hard ASH) to further
enhance the learning retention.
Publication Date: 2024-04-26
Categories: cs.LG cs.CV
arXiv paper ID: 2404.17651v1

Title: Machine Learning-based Prediction of Porosity for Concrete Containing Supplementary Cementitious Materials
Summary: Porosity has been identified as the key indicator of the durability
properties of concrete exposed to aggressive environments. This paper applies
ensemble learning to predict porosity of high-performance concrete containing
supplementary cementitious materials. The concrete samples utilized in this
study are characterized by eight composition features including w/b ratio,
binder content, fly ash, GGBS, superplasticizer, coarse/fine aggregate ratio,
curing condition and curing days. The assembled database consists of 240 data
records, featuring 74 unique concrete mixture designs. The proposed machine
learning algorithms are trained on 180 observations (75%) chosen randomly from
the data set and then tested on the remaining 60 observations (25%). The
numerical experiments suggest that the regression tree ensembles can accurately
predict the porosity of concrete from its mixture compositions. Gradient
boosting trees generally outperforms random forests in terms of prediction
accuracy. For random forests, the out-of-bag error based hyperparameter tuning
strategy is found to be much more efficient than k-Fold Cross-Validation.
Publication Date: 2021-12-13
Categories: cs.LG cs.CE
arXiv paper ID: 2112.07353v1

Title: Unsupervised detection of ash dieback disease (Hymenoscyphus fraxineus) using diffusion-based hyperspectral image clustering
Summary: Ash dieback (Hymenoscyphus fraxineus) is an introduced fungal disease that is
causing the widespread death of ash trees across Europe. Remote sensing
hyperspectral images encode rich structure that has been exploited for the
detection of dieback disease in ash trees using supervised machine learning
techniques. However, to understand the state of forest health at
landscape-scale, accurate unsupervised approaches are needed. This article
investigates the use of the unsupervised Diffusion and VCA-Assisted Image
Segmentation (D-VIS) clustering algorithm for the detection of ash dieback
disease in a forest site near Cambridge, United Kingdom. The unsupervised
clustering presented in this work has high overlap with the supervised
classification of previous work on this scene (overall accuracy = 71%). Thus,
unsupervised learning may be used for the remote detection of ash dieback
disease without the need for expert labeling.
Publication Date: 2022-04-19
Categories: cs.CV cs.LG stat.AP
arXiv paper ID: 2204.09041v1

Title: Extremely Simple Activation Shaping for Out-of-Distribution Detection
Summary: The separation between training and deployment of machine learning models
implies that not all scenarios encountered in deployment can be anticipated
during training, and therefore relying solely on advancements in training has
its limits. Out-of-distribution (OOD) detection is an important area that
stress-tests a model's ability to handle unseen situations: Do models know when
they don't know? Existing OOD detection methods either incur extra training
steps, additional data or make nontrivial modifications to the trained network.
In contrast, in this work, we propose an extremely simple, post-hoc, on-the-fly
activation shaping method, ASH, where a large portion (e.g. 90%) of a sample's
activation at a late layer is removed, and the rest (e.g. 10%) simplified or
lightly adjusted. The shaping is applied at inference time, and does not
require any statistics calculated from training data. Experiments show that
such a simple treatment enhances in-distribution and out-of-distribution
distinction so as to allow state-of-the-art OOD detection on ImageNet, and does
not noticeably deteriorate the in-distribution accuracy. Video, animation and
code can be found at: https://andrijazz.github.io/ash
Publication Date: 2022-09-20
Categories: cs.LG cs.CV
arXiv paper ID: 2209.09858v2