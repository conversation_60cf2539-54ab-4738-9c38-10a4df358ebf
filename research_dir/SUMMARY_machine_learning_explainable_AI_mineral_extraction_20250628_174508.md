Title: GFM4MPM: Towards Geospatial Foundation Models for Mineral Prospectivity Mapping
Summary: Machine Learning (ML) for Mineral Prospectivity Mapping (MPM) remains a
challenging problem as it requires the analysis of associations between
large-scale multi-modal geospatial data and few historical mineral commodity
observations (positive labels). Recent MPM works have explored Deep Learning
(DL) as a modeling tool with more representation capacity. However, these
overparameterized methods may be more prone to overfitting due to their
reliance on scarce labeled data. While a large quantity of unlabeled geospatial
data exists, no prior MPM works have considered using such information in a
self-supervised manner. Our MPM approach uses a masked image modeling framework
to pretrain a backbone neural network in a self-supervised manner using
unlabeled geospatial data alone. After pretraining, the backbone network
provides feature extraction for downstream MPM tasks. We evaluated our approach
alongside existing methods to assess mineral prospectivity of Mississippi
Valley Type (MVT) and Clastic-Dominated (CD) Lead-Zinc deposits in North
America and Australia. Our results demonstrate that self-supervision promotes
robustness in learned features, improving prospectivity predictions.
Additionally, we leverage explainable artificial intelligence techniques to
demonstrate that individual predictions can be interpreted from a geological
perspective.
Publication Date: 2024-06-18
Categories: cs.LG cs.CV
arXiv paper ID: 2406.12756v1

Title: Insights into Lunar Mineralogy: An Unsupervised Approach for Clustering of the Moon Mineral Mapper (M3) spectral data
Summary: This paper presents a novel method for mapping spectral features of the Moon
using machine learning-based clustering of hyperspectral data from the Moon
Mineral Mapper (M3) imaging spectrometer. The method uses a convolutional
variational autoencoder to reduce the dimensionality of the spectral data and
extract features of the spectra. Then, a k-means algorithm is applied to
cluster the latent variables into five distinct groups, corresponding to
dominant spectral features, which are related to the mineral composition of the
Moon's surface. The resulting global spectral cluster map shows the
distribution of the five clusters on the Moon, which consist of a mixture of,
among others, plagioclase, pyroxene, olivine, and Fe-bearing minerals across
the Moon's surface. The clusters are compared to the mineral maps from the
Kaguya mission, which showed that the locations of the clusters overlap with
the locations of high wt% of minerals such as plagioclase, clinopyroxene, and
olivine. The paper demonstrates the usefulness of unbiased unsupervised
learning for lunar mineral exploration and provides a comprehensive analysis of
lunar mineralogy.
Publication Date: 2024-11-05
Categories: astro-ph.EP astro-ph.IM cs.LG
arXiv paper ID: 2411.03186v1

Title: Opening the Black-Box: A Systematic Review on Explainable AI in Remote Sensing
Summary: In recent years, black-box machine learning approaches have become a dominant
modeling paradigm for knowledge extraction in remote sensing. Despite the
potential benefits of uncovering the inner workings of these models with
explainable AI, a comprehensive overview summarizing the explainable AI methods
used and their objectives, findings, and challenges in remote sensing
applications is still missing. In this paper, we address this gap by performing
a systematic review to identify the key trends in the field and shed light on
novel explainable AI approaches and emerging directions that tackle specific
remote sensing challenges. We also reveal the common patterns of explanation
interpretation, discuss the extracted scientific insights, and reflect on the
approaches used for the evaluation of explainable AI methods. As such, our
review provides a complete summary of the state-of-the-art of explainable AI in
remote sensing. Further, we give a detailed outlook on the challenges and
promising research directions, representing a basis for novel methodological
development and a useful starting point for new researchers in the field.
Publication Date: 2024-02-21
Categories: cs.LG
arXiv paper ID: 2402.13791v2

Title: Notes on Applicability of Explainable AI Methods to Machine Learning Models Using Features Extracted by Persistent Homology
Summary: Data analysis that uses the output of topological data analysis as input for
machine learning algorithms has been the subject of extensive research. This
approach offers a means of capturing the global structure of data. Persistent
homology (PH), a common methodology within the field of TDA, has found
wide-ranging applications in machine learning. One of the key reasons for the
success of the PH-ML pipeline lies in the deterministic nature of feature
extraction conducted through PH. The ability to achieve satisfactory levels of
accuracy with relatively simple downstream machine learning models, when
processing these extracted features, underlines the pipeline's superior
interpretability. However, it must be noted that this interpretation has
encountered issues. Specifically, it fails to accurately reflect the feasible
parameter region in the data generation process, and the physical or chemical
constraints that restrict this process. Against this backdrop, we explore the
potential application of explainable AI methodologies to this PH-ML pipeline.
We apply this approach to the specific problem of predicting gas adsorption in
metal-organic frameworks and demonstrate that it can yield suggestive results.
The codes to reproduce our results are available at
https://github.com/naofumihama/xai_ph_ml
Publication Date: 2023-10-15
Categories: cs.LG cs.AI
arXiv paper ID: 2310.09780v1

Title: Extracting PAC Decision Trees from Black Box Binary Classifiers: The Gender Bias Study Case on BERT-based Language Models
Summary: Decision trees are a popular machine learning method, known for their
inherent explainability. In Explainable AI, decision trees can be used as
surrogate models for complex black box AI models or as approximations of parts
of such models. A key challenge of this approach is determining how accurately
the extracted decision tree represents the original model and to what extent it
can be trusted as an approximation of their behavior. In this work, we
investigate the use of the Probably Approximately Correct (PAC) framework to
provide a theoretical guarantee of fidelity for decision trees extracted from
AI models. Based on theoretical results from the PAC framework, we adapt a
decision tree algorithm to ensure a PAC guarantee under certain conditions. We
focus on binary classification and conduct experiments where we extract
decision trees from BERT-based language models with PAC guarantees. Our results
indicate occupational gender bias in these models.
Publication Date: 2024-12-13
Categories: cs.AI cs.LG
arXiv paper ID: 2412.10513v1