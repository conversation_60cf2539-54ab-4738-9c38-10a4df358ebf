Title: Peeking Inside the Black Box: Visualizing Statistical Learning with Plots of Individual Conditional Expectation
Summary: This article presents Individual Conditional Expectation (ICE) plots, a tool
for visualizing the model estimated by any supervised learning algorithm.
Classical partial dependence plots (PDPs) help visualize the average partial
relationship between the predicted response and one or more features. In the
presence of substantial interaction effects, the partial response relationship
can be heterogeneous. Thus, an average curve, such as the PDP, can obfuscate
the complexity of the modeled relationship. Accordingly, ICE plots refine the
partial dependence plot by graphing the functional relationship between the
predicted response and the feature for individual observations. Specifically,
ICE plots highlight the variation in the fitted values across the range of a
covariate, suggesting where and to what extent heterogeneities might exist. In
addition to providing a plotting suite for exploratory analysis, we include a
visual test for additive structure in the data generating model. Through
simulated examples and real data sets, we demonstrate how ICE plots can shed
light on estimated models in ways PDPs cannot. Procedures outlined are
available in the R package ICEbox.
Publication Date: 2013-09-25
Categories: stat.AP
arXiv paper ID: 1309.6392v2

Title: Visualizing the Effects of Predictor Variables in Black Box Supervised Learning Models
Summary: When fitting black box supervised learning models (e.g., complex trees,
neural networks, boosted trees, random forests, nearest neighbors, local
kernel-weighted methods, etc.), visualizing the main effects of the individual
predictor variables and their low-order interaction effects is often important,
and partial dependence (PD) plots are the most popular approach for
accomplishing this. However, PD plots involve a serious pitfall if the
predictor variables are far from independent, which is quite common with large
observational data sets. Namely, PD plots require extrapolation of the response
at predictor values that are far outside the multivariate envelope of the
training data, which can render the PD plots unreliable. Although marginal
plots (M plots) do not require such extrapolation, they produce substantially
biased and misleading results when the predictors are dependent, analogous to
the omitted variable bias in regression. We present a new visualization
approach that we term accumulated local effects (ALE) plots, which inherits the
desirable characteristics of PD and M plots, without inheriting their preceding
shortcomings. Like M plots, ALE plots do not require extrapolation; and like PD
plots, they are not biased by the omitted variable phenomenon. Moreover, ALE
plots are far less computationally expensive than PD plots.
Publication Date: 2016-12-27
Categories: stat.ME
arXiv paper ID: 1612.08468v2

Title: Ab-initio calculation of point defect equilibria during heat treatment: Nitrogen, hydrogen, and silicon doped diamond
Summary: Point defects are responsible for a wide range of optoelectronic properties
in materials, making it crucial to engineer their concentrations for novel
materials design. However, considering the plethora of defects in co-doped
semiconducting and dielectric materials and the dependence of defect formation
energies on heat treatment parameters, process design based on an experimental
trial and error approach is not an efficient strategy. This makes it necessary
to explore computational pathways for predicting defect equilibria during heat
treatments. The accumulated experimental knowledge on defect transformations in
diamond is unparalleled. Therefore, diamond is an excellent material for
benchmarking computational approaches. By considering nitrogen, hydrogen, and
silicon doped diamond as a model system, we have investigated the pressure
dependence of defect formation energies and calculated the defect equilibria
during heat treatment of diamond through ab-initio calculations. We have
plotted monolithic-Kr\"oger-Vink diagrams for various defects, representing
defect concentrations based on process parameters, such as temperature and
partial pressure of gases used during heat treatments of diamond. The method
demonstrated predicts the majority of experimental data, such as nitrogen
aggregation path leading towards the formation of the B center, annealing of
the B, H3, N3, and NVHx centers at ultra high temperatures, the thermal
stability of the SiV center, and temperature dependence of NV concentration. We
demonstrate the possibility of designing heat treatments for a wide range of
semiconducting and dielectric materials by using a relatively inexpensive yet
robust first principles approach, significantly accelerating defect engineering
and high-throughput novel materials design.
Publication Date: 2021-11-22
Categories: cond-mat.mtrl-sci physics.comp-ph
arXiv paper ID: 2111.11359v1

Title: Forest Floor Visualizations of Random Forests
Summary: We propose a novel methodology, forest floor, to visualize and interpret
random forest (RF) models. RF is a popular and useful tool for non-linear
multi-variate classification and regression, which yields a good trade-off
between robustness (low variance) and adaptiveness (low bias). Direct
interpretation of a RF model is difficult, as the explicit ensemble model of
hundreds of deep trees is complex. Nonetheless, it is possible to visualize a
RF model fit by its mapping from feature space to prediction space. Hereby the
user is first presented with the overall geometrical shape of the model
structure, and when needed one can zoom in on local details. Dimensional
reduction by projection is used to visualize high dimensional shapes. The
traditional method to visualize RF model structure, partial dependence plots,
achieve this by averaging multiple parallel projections. We suggest to first
use feature contributions, a method to decompose trees by splitting features,
and then subsequently perform projections. The advantages of forest floor over
partial dependence plots is that interactions are not masked by averaging. As a
consequence, it is possible to locate interactions, which are not visualized in
a given projection. Furthermore, we introduce: a goodness-of-visualization
measure, use of colour gradients to identify interactions and an out-of-bag
cross validated variant of feature contributions.
Publication Date: 2016-05-30
Categories: stat.ML cs.LG
arXiv paper ID: 1605.09196v3

Title: Automated Dependence Plots
Summary: In practical applications of machine learning, it is necessary to look beyond
standard metrics such as test accuracy in order to validate various qualitative
properties of a model. Partial dependence plots (PDP), including
instance-specific PDPs (i.e., ICE plots), have been widely used as a visual
tool to understand or validate a model. Yet, current PDPs suffer from two main
drawbacks: (1) a user must manually sort or select interesting plots, and (2)
PDPs are usually limited to plots along a single feature. To address these
drawbacks, we formalize a method for automating the selection of interesting
PDPs and extend PDPs beyond showing single features to show the model response
along arbitrary directions, for example in raw feature space or a latent space
arising from some generative model. We demonstrate the usefulness of our
automated dependence plots (ADP) across multiple use-cases and datasets
including model selection, bias detection, understanding out-of-sample
behavior, and exploring the latent space of a generative model.
Publication Date: 2019-12-02
Categories: cs.LG stat.ML
arXiv paper ID: 1912.01108v3