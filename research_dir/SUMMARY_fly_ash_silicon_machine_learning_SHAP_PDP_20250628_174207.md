Title: Conditional expectation network for SHAP
Summary: A very popular model-agnostic technique for explaining predictive models is
the SHapley Additive exPlanation (SHAP). The two most popular versions of SHAP
are a conditional expectation version and an unconditional expectation version
(the latter is also known as interventional SHAP). Except for tree-based
methods, usually the unconditional version is used (for computational reasons).
We provide a (surrogate) neural network approach which allows us to efficiently
calculate the conditional version for both neural networks and other regression
models, and which properly considers the dependence structure in the feature
components. This proposal is also useful to provide drop1 and anova analyses in
complex regression models which are similar to their generalized linear model
(GLM) counterparts, and we provide a partial dependence plot (PDP) counterpart
that considers the right dependence structure in the feature components.
Publication Date: 2023-07-20
Categories: cs.LG cs.CE stat.AP stat.ML 62J10, 62J12 I.6.4; I.2.6; G.3
arXiv paper ID: 2307.10654v1

Title: PiML Toolbox for Interpretable Machine Learning Model Development and Diagnostics
Summary: PiML (read $$-ML, /`pai`em`el/) is an integrated and open-access Python
toolbox for interpretable machine learning model development and model
diagnostics. It is designed with machine learning workflows in both low-code
and high-code modes, including data pipeline, model training and tuning, model
interpretation and explanation, and model diagnostics and comparison. The
toolbox supports a growing list of interpretable models (e.g. GAM, GAMI-Net,
XGB1/XGB2) with inherent local and/or global interpretability. It also supports
model-agnostic explainability tools (e.g. PFI, PDP, LIME, SHAP) and a powerful
suite of model-agnostic diagnostics (e.g. weakness, reliability, robustness,
resilience, fairness). Integration of PiML models and tests to existing MLOps
platforms for quality assurance are enabled by flexible high-code APIs.
Furthermore, PiML toolbox comes with a comprehensive user guide and hands-on
examples, including the applications for model development and validation in
banking. The project is available at
https://github.com/SelfExplainML/PiML-Toolbox.
Publication Date: 2023-05-07
Categories: cs.LG
arXiv paper ID: 2305.04214v3

Title: PDP: Parameter-free Differentiable Pruning is All You Need
Summary: DNN pruning is a popular way to reduce the size of a model, improve the
inference latency, and minimize the power consumption on DNN accelerators.
However, existing approaches might be too complex, expensive or ineffective to
apply to a variety of vision/language tasks, DNN architectures and to honor
structured pruning constraints. In this paper, we propose an efficient yet
effective train-time pruning scheme, Parameter-free Differentiable Pruning
(PDP), which offers state-of-the-art qualities in model size, accuracy, and
training cost. PDP uses a dynamic function of weights during training to
generate soft pruning masks for the weights in a parameter-free manner for a
given pruning target. While differentiable, the simplicity and efficiency of
PDP make it universal enough to deliver state-of-the-art
random/structured/channel pruning results on various vision and natural
language tasks. For example, for MobileNet-v1, PDP can achieve 68.2% top-1
ImageNet1k accuracy at 86.6% sparsity, which is 1.7% higher accuracy than those
from the state-of-the-art algorithms. Also, PDP yields over 83.1% accuracy on
Multi-Genre Natural Language Inference with 90% sparsity for BERT, while the
next best from the existing techniques shows 81.5% accuracy. In addition, PDP
can be applied to structured pruning, such as N:M pruning and channel pruning.
For 1:4 structured pruning of ResNet18, PDP improved the top-1 ImageNet1k
accuracy by over 3.6% over the state-of-the-art. For channel pruning of
ResNet50, PDP reduced the top-1 ImageNet1k accuracy by 0.6% from the
state-of-the-art.
Publication Date: 2023-05-18
Categories: cs.LG cs.AI cs.CV
arXiv paper ID: 2305.11203v3

Title: Automated Dependence Plots
Summary: In practical applications of machine learning, it is necessary to look beyond
standard metrics such as test accuracy in order to validate various qualitative
properties of a model. Partial dependence plots (PDP), including
instance-specific PDPs (i.e., ICE plots), have been widely used as a visual
tool to understand or validate a model. Yet, current PDPs suffer from two main
drawbacks: (1) a user must manually sort or select interesting plots, and (2)
PDPs are usually limited to plots along a single feature. To address these
drawbacks, we formalize a method for automating the selection of interesting
PDPs and extend PDPs beyond showing single features to show the model response
along arbitrary directions, for example in raw feature space or a latent space
arising from some generative model. We demonstrate the usefulness of our
automated dependence plots (ADP) across multiple use-cases and datasets
including model selection, bias detection, understanding out-of-sample
behavior, and exploring the latent space of a generative model.
Publication Date: 2019-12-02
Categories: cs.LG stat.ML
arXiv paper ID: 1912.01108v3

Title: Financial Fraud Detection Using Explainable AI and Stacking Ensemble Methods
Summary: Traditional machine learning models often prioritize predictive accuracy,
often at the expense of model transparency and interpretability. The lack of
transparency makes it difficult for organizations to comply with regulatory
requirements and gain stakeholders trust. In this research, we propose a fraud
detection framework that combines a stacking ensemble of well-known gradient
boosting models: XGBoost, LightGBM, and CatBoost. In addition, explainable
artificial intelligence (XAI) techniques are used to enhance the transparency
and interpretability of the model's decisions. We used SHAP (SHapley Additive
Explanations) for feature selection to identify the most important features.
Further efforts were made to explain the model's predictions using Local
Interpretable Model-Agnostic Explanation (LIME), Partial Dependence Plots
(PDP), and Permutation Feature Importance (PFI). The IEEE-CIS Fraud Detection
dataset, which includes more than 590,000 real transaction records, was used to
evaluate the proposed model. The model achieved a high performance with an
accuracy of 99% and an AUC-ROC score of 0.99, outperforming several recent
related approaches. These results indicate that combining high prediction
accuracy with transparent interpretability is possible and could lead to a more
ethical and trustworthy solution in financial fraud detection.
Publication Date: 2025-05-15
Categories: cs.LG cs.AI
arXiv paper ID: 2505.10050v1