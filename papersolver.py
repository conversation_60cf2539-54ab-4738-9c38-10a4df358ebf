import random
import string
from utils import *
from tools import *
from copy import copy
from inference import *
from pathlib import Path
from copy import deepcopy
from common_imports import *
from agents import get_score
from abc import abstractmethod

from contextlib import contextmanager
import sys, os

@contextmanager
def suppress_stdout():
    with open(os.devnull, "w") as devnull:
        old_stdout = sys.stdout
        sys.stdout = devnull
        try:
            yield
        finally:
            sys.stdout = old_stdout

class Command:
    def __init__(self):
        self.cmd_type = "OTHER"

    @abstractmethod
    def docstring(self) -> str:
        pass

    @abstractmethod
    def execute_command(self, *args) -> str:
        pass

    @abstractmethod
    def matches_command(self, cmd_str) -> bool:
        pass

    @abstractmethod
    def parse_command(self, *args) -> tuple:
        pass


def execute_latex():
    return True


"""
@@@@@@@@@@@@@@@@@@
@@ SEARCH TOOLS @@
@@@@@@@@@@@@@@@@@@
"""

class Arxiv(Command):
    def __init__(self):
        super().__init__()
        self.arxiv_eng = ArxivSearch()
        self.num_papers_per_search = 10
        self.cmd_type = "SEARCH-arxiv"

    def docstring(self) -> str:
        return (
            "============= ARXIV SEARCH TOOL ============="
            "You also have access to machine learning paper from Arxiv. "
            "To search for summaries of papers on arxiv you can use the following command: ```SUMMARY\n<search query>\n```\n where <search query> is a string that will be used as the search query to find papers with semantically similar content and SUMMARY is just the word SUMMARY.\n"
            "To get the full paper text for an arXiv paper, use the following command: ```FULL_TEXT\n<arxiv paper id>\n```\n where <arxiv paper id> is the ID of the arXiv paper (which can be found by using the SUMMARY command), and FULL_TEXT is just the word FULL_TEXT. Make sure to read the full text using the FULL_TEXT command before adding it to your list of relevant papers.\n"
            "When you read arxiv paper, make sure to take note of the techniques they are using to solve their problem as well as the hyperparameters and implementation details. These are very important for successfully solving machine learning problems."
        )

    def execute_command(self, *args) -> str:
        # args[0] -> command
        # args[1] -> query
        if args[0] == "SUMMARY":
            return self.arxiv_eng.find_papers_by_str(args[1], self.num_papers_per_search)
        elif args[0] == "FULL_TEXT":
            return self.arxiv_eng.retrieve_full_paper_text(args[1])
        raise Exception("Invalid Arxiv Search")

    def matches_command(self, cmd_str) -> bool:
        if "```SUMMARY" in cmd_str: return True
        elif "```FULL_TEXT" in cmd_str: return True
        return False

    def parse_command(self, *args) -> tuple:
        sum_text = extract_prompt(args[0], "SUMMARY")
        full_text = extract_prompt(args[0], "FULL_TEXT")
        
        sum_text = sum_text.split("\n") if sum_text is not None else []
        full_text = full_text.split("\n") if full_text is not None else []
        
        if len(sum_text) == 0 and len(full_text) == 0: return False, None
        if len(sum_text) > 0: return True, ("SUMMARY", sum_text,)
        if len(full_text) > 0: return True, ("FULL_TEXT", full_text,)


"""
@@@@@@@@@@@@@@@@@@@
@@ WRITING TOOLS @@
@@@@@@@@@@@@@@@@@@@
"""

class PaperReplace(Command):
   def __init__(self):
       super().__init__()
       self.cmd_type = "PAPER-replace"

   def docstring(self) -> str:
       return (
           "============= PAPER REPLACING TOOL =============\n"
           "You also have access to a paper replacing tool. \n"
           "This tool allows you to entirely re-write/replace all of the current document content and erase all existing content.\n"
           "You can use this tool via the following command: ```REPLACE\n<content here>\n```, where REPLACE is the word REPLACE and <content here> will be the new content that is replacing the entire document. This content will be automatically converted to Markdown format. This tool is useful if you want to make very significant changes, such as entirely changing the model, or the learning process. Before changing the existing content to be your new content, your new content will be tested and if it returns an error it will not replace the existing content. Try limiting the use of rewriting and aim for editing the content more."
       )

   def execute_command(self, *args) -> str:
       # args[0] -> new content
       args = args[0]
       if isinstance(args[0], str):
           return args[0].split('\n')
       return args[0]

   def matches_command(self, cmd_str) -> bool:
       if "```REPLACE" in cmd_str: return True
       return False

   def parse_command(self, *args) -> tuple:
       new_content = extract_prompt(args[0], "REPLACE")
       if new_content is None:
           return False, (None, "Error: No REPLACE block found in the response.")
       # 使用Markdown编译而不是DOCX
       from utils import compile_markdown
       markdown_ret = compile_markdown(new_content, "research_paper.md")
       if "[MARKDOWN COMPILATION ERROR]" in markdown_ret:
           return False, (None, markdown_ret,)
       return True, (new_content.split("\n"), markdown_ret)


class PaperEdit(Command):
   def __init__(self):
       super().__init__()
       self.cmd_type = "PAPER-edit"

   def docstring(self) -> str:
       return (
           "============= PAPER EDITING TOOL =============\n"
           "You also have access to a paper editing tool. \n"
           "This tool allows you to replace lines indexed n through m (n:m) of the current document content with as many lines of new content as you want to add. This removal is inclusive meaning that line n and m and everything between n and m is removed. This will be the primary way that you interact with the document. \n"
           "You can edit the document using the following command: ```EDIT N M\n<new lines to replace old lines>\n``` EDIT is the word EDIT, N is the first line index you want to replace and M the the last line index you want to replace (everything inbetween will also be removed), and <new lines to replace old lines> will be the new content that is replacing the old content. Before changing the existing content to be your new content, your new content will be tested and if it returns an error it will not replace the existing content. Your changes should significantly change the content. You should write new paragraphs and update old ones. Try using the edit command often. Make sure to generate lots of text. You should also avoid editing lines 0 0, and should edit the main text of the paragraphs, such as editing lines in the middle of the text body."
       )

   def execute_command(self, *args) -> str:
       # args[0] -> N (int)
       # args[1] -> M (int)
       # args[2] -> old content
       # args[3] -> new lines to replace
       try:
           args = args[0]
           current_content = args[2]
           lines_to_add = list(reversed(args[3]))
           lines_to_replace = list(reversed(range(args[0], args[1]+1)))
           for _ln in lines_to_replace:
               current_content.pop(_ln)
           for _line in lines_to_add:
               current_content.insert(args[0], _line)
           new_content = "\n".join(current_content)
           
           # 生成Markdown文件
           from utils import compile_markdown
           markdown_ret = compile_markdown(new_content, "research_paper.md")
           
           if "error" in markdown_ret.lower():
               return (False, None, markdown_ret)
           return (True, current_content, markdown_ret)
       except Exception as e:
           return (False, None, str(e))

   def matches_command(self, cmd_str) -> bool:
       if "```EDIT" in cmd_str: return True
       return False

   def parse_command(self, *args) -> tuple:
       cmd_str, content_lines = args[0], args[1]
       success = True
       try:
           text = extract_prompt(cmd_str, "EDIT").split("\n")
           if len(text) == 0: return False, (None, None, None, None)
           lines_to_edit = text[0].split(" ")
           if len(lines_to_edit) != 2: return False, (None, None, None, None)
           lines_to_edit = [int(_) for _ in lines_to_edit]
           if len(text[1:]) == 0: return False, (None, None, None, None)
           return success, (lines_to_edit[0], lines_to_edit[1], content_lines, text[1:])
       except Exception as e:
           return False, (None, None, None, None)




# Modified version of section tips from the AI scientist paper!
# Good work guys :) https://github.com/SakanaAI/AI-Scientist/blob/main/ai_scientist/perform_writeup.py
per_section_tips = {
    "abstract": """
- TL;DR of the paper
- What are we trying to do and why is it relevant?
- Why is this hard? 
- How do we solve it (i.e. our contribution!)
- How do we verify that we solved it (e.g. Experiments and results)
- This must only be a single paragraph not more.

Please make sure the abstract reads smoothly and is well-motivated. This should be one continuous paragraph with no breaks between the lines.
""",
    "introduction": """
- Longer version of the Abstract, i.e. of the entire paper
- What are we trying to do and why is it relevant?
- Why is this hard? 
- How do we solve it (i.e. our contribution!)
- How do we verify that we solved it (e.g. Experiments and results)
- New trend: specifically list your contributions as bullet points
- Extra space? Future work!
""",
    "related work": """
- Academic siblings of our work, i.e. alternative attempts in literature at trying to solve the same problem. 
- Goal is to “Compare and contrast” - how does their approach differ in either assumptions or method? If their method is applicable to our Problem Setting I expect a comparison in the experimental section. If not, there needs to be a clear statement why a given method is not applicable. 
- Note: Just describing what another paper is doing is not enough. We need to compare and contrast.
""",
    "background": """
- Academic Ancestors of our work, i.e. all concepts and prior work that are required for understanding our method. 
- Usually includes a subsection, Problem Setting, which formally introduces the problem setting and notation (Formalism) for our method. Highlights any specific assumptions that are made that are unusual. 
- Make sure to use mathematical notation when necessary.
- Note: If our paper introduces a novel problem setting as part of its contributions, it's best to have a separate Section.
""",
    "methods": """
- What we do. Why we do it. All described using the general Formalism introduced in the Problem Setting and building on top of the concepts / foundations introduced in Background.
- Make sure you clearly report precise mathematical equations in the methods section and the precise methodology.
""",
    "experimental setup": """
- How do we test that our stuff works? Introduces a specific instantiation of the Problem Setting and specific implementation details of our Method for this Problem Setting.
- Do not imagine unknown hardware details.
- Includes a description of the dataset, evaluation metrics, important hyperparameters, and implementation details.
""",
    "results": """
- Shows the results of running Method on our problem described in Experimental Setup.
- Includes statements on hyperparameters and other potential issues of fairness.
- Only includes results that have actually been run and saved in the logs. Do not hallucinate results that don't exist.
- Make sure you clearly and numerically report experimental results in the results section.
- If results exist: compares to baselines and includes statistics and confidence intervals. 
- If results exist: includes ablation studies to show that specific parts of the method are relevant.
- Discusses limitations of the method.
- Make sure to include all the results from the experiments, and include all relevant figures.
""",
    "discussion": """
- Brief recap of the entire paper.
- To keep going with the analogy, you can think of future work as (potential) academic offspring.
""",
}

class PaperSolver:
    def __init__(self, llm_str, notes=None, max_steps=10, insights=None, plan=None, exp_code=None, exp_results=None, lit_review=None, ref_papers=None, topic=None, openai_api_key=None, openai_base_url=None, openai_model_name=None, compile_pdf=True, author_name="Agent Laboratory", generated_images=None): # Added generated_images
        if notes is None: self.notes = []
        else: self.notes = notes
        if plan is None: self.plan = ""
        else: self.plan = plan
        self.author_name = author_name # Add author_name
        if exp_code is None: self.exp_code = ""
        else: self.exp_code = exp_code
        if exp_results is None: self.exp_results = ""
        else: self.exp_results = exp_results
        if lit_review is None: self.lit_review = ""
        else: self.lit_review = lit_review
        if insights is None: self.insights = ""
        else: self.insights = insights
        if ref_papers is None: self.ref_papers = ""
        else: self.ref_papers = ref_papers
        if topic is None: self.topic = ""
        else: self.topic = topic
        self.compile_pdf = compile_pdf
        self.llm_str = llm_str
        self.notes = notes
        self.max_papers = 1
        self.st_hist_len = 10
        self.min_gen_trials = 2
        self.max_steps = max_steps
        self.document_lines = []
        self.prev_document_ret = str()
        self.section_related_work = {}
        self.openai_api_key = openai_api_key
        self.openai_base_url = openai_base_url
        self.openai_model_name = openai_model_name
        self.generated_images = generated_images if generated_images else [] # Store generated images

    def solve(self):
        num_attempts = 0
        best_pkg = None
        top_score = None
        self.prev_document_ret = None
        while True:
            # 确保document_lines始终是列表
            selected_report = copy(random.choice(self.best_report)[0])
            if isinstance(selected_report, list):
                self.document_lines = selected_report
            else:
                # 如果不是列表，初始化为空列表
                print(f"Warning: selected_report is {type(selected_report).__name__}, initializing empty document_lines")
                self.document_lines = []
            model_resp = query_model(
                model_str=self.model,
                system_prompt=self.system_prompt(),
                prompt=f"\nNow please enter a command: ",
                temp=1.0,
                openai_api_key=self.openai_api_key,
                openai_base_url=self.openai_base_url,
                openai_model_name=self.openai_model_name)
            #print(model_resp)
            model_resp = self.clean_text(model_resp)
            cmd_str, document_lines, prev_document_ret, score = self.process_command(model_resp)
            if score is not None:
                if top_score is None:
                    best_pkg = copy(document_lines), copy(prev_document_ret), copy(model_resp), copy(cmd_str)
                    top_score = score
                elif score > top_score:
                    best_pkg = copy(document_lines), copy(prev_document_ret), copy(model_resp), copy(cmd_str)
                    top_score = score
            if num_attempts >= self.min_gen_trials and top_score is not None: break
            print(f"@@@ Command Exec // Attempt {num_attempts}: ", str(cmd_str).replace("\n", " | "))
            print(f"$$$ Score: {score}")
            num_attempts += 1
        self.document_lines, self.prev_document_ret, model_resp, cmd_str = best_pkg
        # add top scoring paper that was successful to the best papers
        if top_score is not None and self.best_report and self.best_report[-1][1] is not None and top_score > self.best_report[-1][1]:
            # replace the lowest scoring one
            if len(self.best_report) >= self.max_papers:
                self.best_report.pop(-1)
            self.best_report.append((copy(self.document_lines), copy(top_score), self.prev_document_ret))
            # sort by score, to make sure lowest are removed in future
            self.best_report.sort(key=lambda x: x[1], reverse=True)
        return model_resp, cmd_str

    def initial_solve(self):
        """
        Initialize the solver and get an initial set of papers and a return
        @return: None
        """
        # @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        # @@ Initial PaperGen Commands @@
        # @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        self.best_score = None
        self.commands = [PaperReplace()]
        self.model = f"{self.llm_str}"
        init_report, init_return, self.best_score = self.gen_initial_report()
        self.best_report = [(copy(init_report), self.best_score, init_return) for _ in range(1)]

        self.document_lines = init_report
        self.model = f"{self.llm_str}"
        self.commands = [PaperEdit()] #, Replace()]
        self.prev_working_report = copy(self.document_lines)

    @staticmethod
    def clean_text(text):
        text = text.replace("```\n", "```")
        return text

    def gen_initial_report(self):
        num_attempts = 0
        arx = ArxivSearch()
        section_scaffold = str()
        #  1. Abstract 2. Introduction, 3. Background, 4. Methods, 5. Experimental Setup 6. Results, and 7. Discussion
        for _section in ["scaffold", "abstract", "introduction", "related work", "background", "methods", "experimental setup", "results", "discussion"]:
            if _section in self.section_related_work and _section != "scaffold":
                continue
            section_complete = False
            if _section in ["introduction", "related work", "background", "methods", "discussion"]:
                attempts = 0
                papers = str()
                first_attempt = True
                while len(papers) == 0:
                    att_str = str()
                    if attempts > 5:
                        break
                    if not first_attempt:
                        att_str = "This is not your first attempt please try to come up with a simpler search query."
                    
                    # Pass openai_base_url and openai_model_name if model is openai-compatible
                    if self.llm_str == "openai-compatible":
                        search_query = query_model(model_str=f"{self.llm_str}", prompt=f"Given the following research topic {self.topic} and research plan: \n\n{self.plan}\n\nPlease come up with a search query to find relevant papers on arXiv. Respond only with the search query and nothing else. This should be a a string that will be used to find papers with semantically similar content. {att_str}", system_prompt=f"You are a research paper finder. You must find papers for the section {_section}. Query must be text nothing else.", openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
                    else:
                        search_query = query_model(model_str=f"{self.llm_str}", prompt=f"Given the following research topic {self.topic} and research plan: \n\n{self.plan}\n\nPlease come up with a search query to find relevant papers on arXiv. Respond only with the search query and nothing else. This should be a a string that will be used to find papers with semantically similar content. {att_str}", system_prompt=f"You are a research paper finder. You must find papers for the section {_section}. Query must be text nothing else.", openai_api_key=self.openai_api_key)
                    
                    search_query.replace('"', '')
                    papers = arx.find_papers_by_str(query=search_query, N=10)
                    first_attempt = False
                    attempts += 1
                if len(papers) != 0:
                    self.section_related_work[_section] = papers
            while not section_complete:
                section_scaffold_temp = copy(section_scaffold)
                if num_attempts == 0: err = str()
                else: err = f"The following was the previous command generated: {model_resp}. This was the error return {cmd_str}. You should make sure not to repeat this error and to solve the presented problem."
                if _section == "scaffold":
                    prompt = f"{err}\nNow please enter the ```REPLACE command to create the scaffolding:\n "
                else:
                    rp = str()
                    if _section in self.section_related_work:
                        rp = f"Here are related papers you can cite: {self.section_related_work[_section]}. You can cite them just by putting the arxiv ID in parentheses, e.g. (arXiv 2308.11483v1)\n"
                    prompt = f"{err}\n{rp}\nNow please enter the ```REPLACE command to create the designated section, make sure to only write the text for that section and nothing else. Do not include packages or section titles, just the section content:\n "
                
                # Pass openai_base_url and openai_model_name if model is openai-compatible
                if self.model == "openai-compatible":
                    model_resp = query_model(
                        model_str=self.model,
                        system_prompt=self.system_prompt(section=_section),
                        prompt=f"{prompt}",
                        temp=0.8,
                        openai_api_key=self.openai_api_key,
                        openai_base_url=self.openai_base_url,
                        openai_model_name=self.openai_model_name)
                else:
                    model_resp = query_model(
                        model_str=self.model,
                        system_prompt=self.system_prompt(section=_section),
                        prompt=f"{prompt}",
                        temp=0.8,
                        openai_api_key=self.openai_api_key)
                
                print(f"DEBUG: model_resp for {_section}:\n{model_resp}") # Added debug print
                model_resp = self.clean_text(model_resp)
                if _section == "scaffold":
                    # minimal scaffold (some other sections can be combined)
                    for _sect in ["[ABSTRACT HERE]", "[INTRODUCTION HERE]", "[METHODS HERE]", "[RESULTS HERE]", "[DISCUSSION HERE]"]:
                        if _sect not in model_resp:
                            cmd_str = "Error: scaffold section placeholders were not present (e.g. [ABSTRACT HERE])."
                            print("@@@ INIT ATTEMPT:", cmd_str)
                            continue
                elif _section != "scaffold":
                    new_text = extract_prompt(model_resp, "REPLACE")
                    if new_text is None:
                        if _section in ["related work", "abstract"]:
                            new_text = "" # Allow empty related work
                        else:
                            cmd_str = f"Error: No REPLACE block found in model response for section {_section}. Please ensure the response contains a properly formatted ```REPLACE block."
                            print("@@@ INIT ATTEMPT:", cmd_str)
                            continue
                    section_scaffold_temp = section_scaffold_temp.replace(f"[{_section.upper()} HERE]", new_text)
                    model_resp = '```REPLACE\n' + copy(section_scaffold_temp) + '\n```'
                    if "documentclass{article}" in new_text or "usepackage{" in new_text:
                            cmd_str = "Error: You must not include packages or documentclass in the text! Your content must only include the section text, equations, and tables."
                            print("@@@ INIT ATTEMPT:", cmd_str)
                            continue
                cmd_str, document_lines, prev_document_ret, score = self.process_command(model_resp, scoring=False)
                print(f"@@@ INIT ATTEMPT: Command Exec // Attempt {num_attempts}: ", str(cmd_str).replace("\n", " | "))
                #print(f"$$$ Score: {score}")
                if score is not None:
                    section_complete = True
                    section_scaffold = "\n".join(document_lines)
                num_attempts += 1
            self.document_lines = section_scaffold.split("\n")
            print("$"*10, f"SCAFFOLD [{_section}] CREATED", "$"*10)
        print("$"*10, "SCAFFOLD CREATED", "$"*10)
        return document_lines, prev_document_ret, score

    def process_command(self, model_resp, scoring=True):
        """
        Take command from language model and execute if valid
        @param model_resp: (str) language model output
        @return: (tuple) tuple containing the following items
            - cmd_str: (str) document execution return and success flag
            - document_lines: (list) list of document lines as strings
            - prev_document_ret: (str) output from running document
            - score: (float) score of model
        """
        cmd_str = None
        score = None
        prev_document_ret = self.prev_document_ret
        document_lines = copy(self.document_lines)
        print(f"DEBUG: process_command - model_resp: {model_resp}")
        if "\\includegraphics[width=\\textwidth]{Figure_1.png}" in model_resp or "\\includegraphics[width=\\textwidth]{Figure_2.png}" in model_resp:
            cwd = os.getcwd()
            model_resp = model_resp.replace("\\includegraphics[width=\\textwidth]{Figure_1.png}", "\\includegraphics[width=\\textwidth]{" + cwd + "/Figure_1.png}")
            model_resp = model_resp.replace("\\includegraphics[width=\\textwidth]{Figure_2.png}", "\\includegraphics[width=\\textwidth]{" + cwd + "/Figure_2.png}")
        for cmd in self.commands:
            if cmd.matches_command(model_resp):
                # attempt to execute the document edit command
                if cmd.cmd_type == "PAPER-edit": # DONE
                    score = None
                    failed = True
                    success, args = cmd.parse_command(model_resp, document_lines)
                    if success:
                        # True, current_document, document_ret
                        exec_args = cmd.execute_command((args[0], args[1], document_lines, args[3], self.compile_pdf))
                        success = success and exec_args[0]
                        if not success:
                            document_err = f"Return from executing document: {exec_args[2]}"
                        else:
                            document_lines = copy(exec_args[1]) #
                            if scoring:
                                score, cmd_str, is_valid = get_score(self.plan, "\n".join(document_lines), reward_model_llm=self.llm_str, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
                            else:
                                score, cmd_str, is_valid = 0.0, "Document scored successfully", True
                            if is_valid: failed = False
                            document_err = f"Return from scoring document: {cmd_str}"
                        print("$$$$ DOCUMENT EDIT (success)")
                    else:
                        document_err = f"Return from parsing command: {args[1]}"
                        print(f"DEBUG: process_command - parsing command failed, document_err: {document_err}")

                    if failed:
                        cmd_str = f"Document edit FAILED due to the following error: {document_err}. Document was reverted back to original state before edits."
                        print("$$$$ DOCUMENT EDIT (failed)")
                        print(f"DEBUG: process_command - cmd_str: {cmd_str}")
                    else:
                        cmd_str = "Document was successfully edited."
                        document_lines = copy(args[1])
                        prev_document_ret = copy(args[2])
                        print("$$$$ DOCUMENT EDIT (success)")
                elif cmd.cmd_type == "PAPER-replace": # DONE
                    score = None
                    failed = True
                    success, args = cmd.parse_command(model_resp, self.compile_pdf)
                    document_err = f"Return from executing document: {args[1]}"
                    if success:
                        document_lines = copy(args[0]) #
                        if scoring:
                            score, cmd_str, is_valid = get_score(self.plan, "\n".join(document_lines), reward_model_llm=self.llm_str, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
                        else:
                            score, cmd_str, is_valid = 0.0, "Document scored successfully", True
                        if is_valid:
                            failed = False
                        else:
                            print(f"DEBUG: process_command - scoring failed, cmd_str: {cmd_str}")
                        document_err += f"\nReturn from executing document: {cmd_str}"
                    if failed:
                        cmd_str = f"Document replacement FAILED due to the following error: {document_err}.  Document was reverted back to original state before edits."
                        print("$$$$ DOCUMENT REPLACE (failed)")
                    else:
                        cmd_str = "Document was successfully replaced."
                        document_lines = copy(args[0])
                        prev_document_ret = copy(args[1])
                        print("$$$$ DOCUMENT REPLACE (success)")
                        print(f"DEBUG: process_command - document replaced successfully")
        return cmd_str, document_lines, prev_document_ret, score

    def generate_document_lines(self, content):
        """
        Generate well-formatted content lines with line numbers
        @param content: (list) list of content line strings
        @return: (str) content lines formatted with line numbers
        """
        # 添加类型检查，确保content是列表
        if not isinstance(content, list):
            # 如果content不是列表，尝试转换或返回空字符串
            if isinstance(content, str):
                content = content.split('\n')
            elif isinstance(content, (int, float)):
                return f"Error: content is {type(content).__name__}: {content}\n"
            else:
                return f"Error: content is unexpected type {type(content).__name__}\n"

        content_str = str()
        for _index in range(len(content)):
            content_str += f"{_index} |{content[_index]}\n"
        return content_str

    def system_prompt(self, commands=True, section=None):
        """
        Produce a system prompt for the document-solver
        @param commands: (bool) whether to use command prompt
        @return: (str) system prompt
        """
        if section == "abstract": length = "This section should be ONLY 1 paragraph."
        else: length = "This section should be approximately 2-4 paragraphs and so your output should be several paragraphs of content."
        methods_str = str() # Keep for other method-specific tips if any, or remove if only for old figures.
        image_insertion_prompt = ""
        if self.generated_images and section in ["methods", "experimental setup", "results", "discussion"]: # Specify sections where images are relevant
            image_insertion_prompt += (
                "\n\n--- IMAGE INSERTION GUIDELINES ---\n"
                "The following figures have been generated and are available for you to include in this section. "
                "Please insert them using Markdown syntax: ![Descriptive Alt Text](relative_path_to_image.png)\n"
                "Ensure the 'Descriptive Alt Text' is relevant to the image content and its context in the paper.\n"
                "The 'relative_path_to_image.png' should be exactly as provided below (these paths are relative to the final report document).\n"
                "Available figures:\n"
            )
            for idx, img_path in enumerate(self.generated_images):
                # Assuming img_path is like "silicon_analysis_plots_YYYYMMDD_HHMMSS/figure_name.png"
                image_insertion_prompt += f"  - Figure {idx+1}: '{img_path}'. Example usage: ![Figure {idx+1} showing X]({img_path})\n"
            image_insertion_prompt += "Integrate these figures naturally within the text where they support your points or illustrate results.\n"
            image_insertion_prompt += "--- END IMAGE INSERTION GUIDELINES ---\n\n"

        if section is not None and section == "scaffold":
            section_cmd = (
                f"Your objective right now is to only build the scaffolding for the document. "
                f"You should not include any text in the body of the document, but should have an empty scaffold for each of the sections. "
                f"Where the sections go, write [ABSTRACT HERE] for abstract, and write [INTRODUCTION HERE] for the introduction... etc. "
                f"Your document should have the following sections: 1. Abstract 2. Introduction, 3. Background, 4. Related Work 5. Methods, 6. Experimental Setup 7. Results, and 8. Discussion. "
                f"Just create the scaffolding as compilable content. Your title should start with Research Report: [title here] where title here is a title you choose. For author write {self.author_name}."
            )
        elif section is not None:
            section_cmd = (
                f"Your only goal is to generate content for the following {section}. "
                f"DO NOT INCLUDE ANY PACKAGES OR ANY SECTION COMMANDS. DO NOT INCLUDE A TITLE OR DATE ONLY TEXT. "
                f"You only have to generate text for this specific section and do not have to output anything else. "
                f"{length} I repeat DO NOT INCLUDE ANY PACKAGES OR ANY SECTION COMMANDS. DO NOT INCLUDE A TITLE OR DATE ONLY TEXT. "
                f"Use as many equations as you find necessary. You should include mathematical equations, numbers, and tables where necessary. "
                f"Remember that to include a percentage sign % you must add a backslash \\% or else it will become a comment. "
                f"Here are some tips {per_section_tips[section]}  {methods_str}. {image_insertion_prompt}\n\n" # Added image_insertion_prompt
                f"IMPORTANT: You MUST encapsulate your entire response within a ```SECTION_CONTENT\\n<content here>\\n``` block. "
                f"For example, if you are writing the abstract, your entire response should be:\n\n"
                f"```SECTION_CONTENT\n"
                f"This paper introduces a novel approach to...\n"
                f"Our method leverages advanced machine learning techniques to...\n"
                f"We demonstrate the effectiveness of our approach through extensive experiments on...\n"
                f"The results show that our method outperforms existing state-of-the-art approaches by a significant margin.\n"
                f"```\n\n"
                f"The content inside the SECTION_CONTENT block should be a comprehensive and detailed write-up for the {section} section, aiming for substantial length to contribute to the overall 4000-word goal. "
                f"Generate at least 500 words for this section if possible."
            )
        else:
            section_cmd = ""

        if isinstance(self.document_lines, list):
            content_len = sum([i.strip(string.punctuation).isalpha() for i in ("".join(self.document_lines)).split()])
        else:
            content_len = 0
            
        if content_len < 4000:
            content_progress = f"The current length of the document is {content_len} words, you must increase this by {4000-content_len} words. Aim to write long, detailed paragraphs for each section."
        else:
            content_progress = "The document has reached the target length of 4000 words. Focus on refining existing content."
        
        print(content_progress)
        
        cmd_set = f"The following are commands you have access to: {self.command_descriptions()}\n." if commands else ""
        
        ref_papers = ""
        if len(self.ref_papers) > 0:
            refpapers = '\n'.join(self.ref_papers)
            ref_papers = f"Here is a reference paper that is high quality:\n{refpapers}\n\n\n"
        
        lit_review_str = str(self.lit_review)[:20000]
        
        return (
            f"{ref_papers}"
            f"{self.role_description()}.\n"
            f"The following are your task instructions: {self.phase_prompt()}\n"
            f"The following are notes, instructions, and general tips for you: {self.notes}"
            f"The following literature review was provided for the document:\n{lit_review_str}\n"
            f"You are given a document writing task. The original research plan was described as follows: {self.plan}\n"
            f"A team of research wrote the following code, following this plan: {self.exp_code}\n"
            f"After running this code, the following results were observed: {self.exp_results}\n"
            f"Provided was an interpretation of the experimental results:\n{self.insights}\n"
            f"Your writing style should be boring and objective.\n"
            f"Your goal is to write a document as well as possible. You will receive a score after you write the document and should aim to maximize the score by writing a high quality document. The document length should be 8 pages or 4000 words in total. It should be quite long and comprehensive. Remember, the document MUST BE LONG. {content_progress}\n"
            f"{cmd_set}\n"
            f"Provided here is your current document {self.generate_document_lines(self.document_lines)}"
            f"{section_cmd}"
        )

    def command_descriptions(self):
        """
        Provide command descriptions
        @return: (str) command descriptions
        """
        cmd_strings = "\n".join([_cmd.docstring() for _cmd in self.commands])
        return f"\nYou also have access to tools which can be interacted with using the following structure: ```COMMAND\n<command information here>\n```, where COMMAND is whichever command you want to run (e.g. EDIT,...), <command information here> is information used for the command and ``` are meant to encapsulate the command. ``` must be included as part of the command both at the beginning and at the end of the command. DO NOT FORGOT TO HAVE ``` AT THE TOP AND BOTTOM OF COMMAND. and this structure must be followed to execute a command correctly. YOU CAN ONLY EXECUTE A SINGLE COMMAND AT A TIME! Do not try to perform multiple commands EVER only one." + cmd_strings

    def role_description(self):
        """
        Provide role description
        @return: (str) role description
        """
        return "You are a computer science PhD student at a top university who has submitted their paper to an ML conference called ICLR. Your goal was to write a document and get high scores from the reviewers so that it get accepted to the conference. Your document should be approximately 8 pages and around 4000 words. Your article should ONLY CONTAIN EIGHT sections as follows: 1. Abstract 2. Introduction, 3. Background, 4. Related Work 5. Methods, 6. Experimental Setup 7. Results, and 8. Discussion.\n"


    def phase_prompt(self,):
        """
        Describe system role and general tips for mle-solver
        @return: (str) system role
        """
        phase_str = (
            "You are a PhD student who has submitted their paper to an ML conference called ICLR. Your goal was to write a document and get high scores from the reviewers so that it get accepted to the conference.\n"
        )
        return phase_str
