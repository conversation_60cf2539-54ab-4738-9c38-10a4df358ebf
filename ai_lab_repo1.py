from agents import *
from utils import save_text_to_markdown # Added import for saving to Markdown
from copy import copy
import sys
import os
sys.path.append(os.path.abspath('.'))
from common_imports import *
from mlesolver import MLESolver
from torch.backends.mkl import verbose
import re
import glob
from pathlib import Path

import argparse
import pickle

DEFAULT_LLM_BACKBONE = "gemini-2.5-pro" # Updated to a more stable Gemini model


class LaboratoryWorkflow:
    def __init__(self, research_topic, openai_api_key, openai_base_url=None, openai_model_name=None, max_steps=100, num_papers_lit_review=5, agent_model_backbone=f"{DEFAULT_LLM_BACKBONE}", notes=list(), human_in_loop_flag=None, compile_pdf=True, mlesolver_max_steps=3, papersolver_max_steps=5, args=None, author_name="Agent Laboratory"):
        # Added start_subtask for resuming workflow
        self.start_subtask = None
        """
        初始化实验室工作流
        @param research_topic: (str) 待探索的研究想法描述
        @param max_steps: (int) 每个阶段的最大步数，即计算容忍度预算
        @param num_papers_lit_review: (int) 文献综述中包含的论文数量
        @param agent_model_backbone: (str 或 dict) 用于智能体的模型骨干
        @param notes: (list) 智能体在任务中遵循的注意事项
        """

        self.notes = notes
        self.max_steps = max_steps
        self.compile_pdf = compile_pdf
        self.openai_api_key = openai_api_key
        self.openai_base_url = openai_base_url
        self.openai_model_name = openai_model_name
        self.research_topic = research_topic
        self.model_backbone = agent_model_backbone
        self.num_papers_lit_review = num_papers_lit_review
        self.args = args  # Store args for later use
        self.author_name = author_name # Store author_name

        self.print_cost = True
        self.cost = 0.0  # Initialize cost tracking
        self.review_override = True # should review be overridden?
        self.review_ovrd_steps = 0 # review steps so far
        self.arxiv_paper_exp_time = 3
        self.reference_papers = list()
        self.semantic_references = list()  # Store semantic scholar references

        ##########################################
        ####### COMPUTE BUDGET PARAMETERS ########
        ##########################################
        self.num_ref_papers = 1
        self.review_total_steps = 0 # num steps to take if overridden
        self.arxiv_num_summaries = num_papers_lit_review  # Use the parameter passed from command line
        self.mlesolver_max_steps = mlesolver_max_steps
        self.papersolver_max_steps = papersolver_max_steps

        self.phases = [
            ("literature review", ["literature review"]),
            # ("visualization", ["visualization"]),  # 跳过visualization阶段，避免重复执行visualization_utils.py
            ("plan formulation", ["plan formulation"]),
            ("experimentation", ["data preparation", "running experiments"]),
            ("results interpretation", ["results interpretation", "report writing", "report refinement"]),
        ]
        self.phase_status = dict()
        for phase, subtasks in self.phases:
            for subtask in subtasks:
                self.phase_status[subtask] = False

        self.phase_models = dict()
        if type(agent_model_backbone) == str:
            for phase, subtasks in self.phases:
                for subtask in subtasks:
                    self.phase_models[subtask] = agent_model_backbone
        elif type(agent_model_backbone) == dict:
            # todo: check if valid
            self.phase_models = agent_model_backbone


        self.human_in_loop_flag = human_in_loop_flag

        self.statistics_per_phase = {
            "literature review":      {"time": 0.0, "steps": 0.0,},
            "plan formulation":       {"time": 0.0, "steps": 0.0,},
            "data preparation":       {"time": 0.0, "steps": 0.0,},
            "running experiments":    {"time": 0.0, "steps": 0.0,},
            "results interpretation": {"time": 0.0, "steps": 0.0,},
            "report writing":         {"time": 0.0, "steps": 0.0,},
            "report refinement":      {"time": 0.0, "steps": 0.0,},
        }

        self.save = True
        self.verbose = True
        self.reviewers = ReviewersAgent(model=self.model_backbone, notes=self.notes, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.phd = PhDStudentAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.postdoc = PostdocAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.professor = ProfessorAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.ml_engineer = MLEngineerAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)

        # Initialize dialogue history for saving
        self.dialogue_history = []
        self.current_phase_dialogues = []

    def set_model(self, model):
        self.set_agent_attr("model", model)
        self.reviewers.model = model

    def save_state(self, phase):
        """
        保存阶段状态
        @param phase: (str) 阶段字符串
        @return: None
        """
        phase = phase.replace(" ", "_")
        with open(f"state_saves/{phase}.pkl", "wb") as f:
            pickle.dump(self, f)
        
        # Save dialogues for this phase
        if len(self.current_phase_dialogues) > 0:
            self.save_phase_dialogues_to_file(phase)

    def save_dialogue(self, speaker, dialogue_content, phase):
        """
        Save dialogue content with timestamp and speaker information
        @param speaker: (str) who is speaking (e.g., "Postdoc", "PhD", "ML Engineer")
        @param dialogue_content: (str) the actual dialogue content
        @param phase: (str) current phase of the research
        @return: None
        """
        import datetime
        
        dialogue_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "phase": phase,
            "speaker": speaker,
            "content": dialogue_content
        }
        
        self.current_phase_dialogues.append(dialogue_entry)
        self.dialogue_history.append(dialogue_entry)
        
        if self.verbose:
            print(f"[DIALOGUE SAVED] {speaker} in {phase}: {dialogue_content[:100]}...")
    
    def save_all_dialogues_to_file(self, filename=None):
        """
        将所有收集到的对话保存到文件
        @param filename: (str) 可选文件名，默认为带时间戳的文件名
        @return: None
        """
        import datetime
        import json
        
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dialogue_history_{timestamp}.json"
        
        filepath = os.path.join("./research_dir", filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.dialogue_history, f, ensure_ascii=False, indent=2)
            print(f"所有对话已保存到: {filepath}")
            print(f"总共保存了 {len(self.dialogue_history)} 条对话记录")
        except Exception as e:
            print(f"保存对话时出错: {str(e)}")
    
    def save_phase_dialogues_to_file(self, phase, filename=None):
        """
        Save dialogues from current phase to a separate file
        @param phase: (str) phase name
        @param filename: (str) optional filename
        @return: None
        """
        import datetime
        import json
        
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dialogue_{phase.replace(' ', '_')}_{timestamp}.json"
        
        filepath = os.path.join("./research_dir", filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.current_phase_dialogues, f, ensure_ascii=False, indent=2)
            print(f"{phase} 阶段的对话已保存到: {filepath}")
            print(f"该阶段保存了 {len(self.current_phase_dialogues)} 条对话记录")
            
            # Clear current phase dialogues for next phase
            self.current_phase_dialogues = []
        except Exception as e:
            print(f"保存 {phase} 阶段对话时出错: {str(e)}")

    def set_agent_attr(self, attr, obj):
        """
        为所有智能体设置属性
        @param attr: (str) 智能体属性
        @param obj: (object) 对象属性
        @return: None
        """
        setattr(self.phd, attr, obj)
        setattr(self.postdoc, attr, obj)
        setattr(self.professor, attr, obj)
        setattr(self.ml_engineer, attr, obj)

    def reset_agents(self):
        """
        Reset all agent states
        @return: None
        """
        self.phd.reset()
        self.postdoc.reset()
        self.professor.reset()
        self.ml_engineer.reset()

    def perform_research(self):
        """
        遍历所有研究阶段
        @return: None
        """
        skip_to_start_subtask = self.start_subtask is not None
        
        for phase, subtasks in self.phases:
            # Only start timing if we are not skipping
            # Also, print "Beginning phase" only if not skipping or if it's the actual starting phase
            if not skip_to_start_subtask or any(s.replace(" ", "_") == self.start_subtask for s in subtasks):
                phase_start_time = time.time()  # Start timing the phase
                if self.verbose: print(f"{'*'*50}\nBeginning phase: {phase}\n{'*'*50}")
            
            for subtask in subtasks:
                if skip_to_start_subtask:
                    if subtask.replace(" ", "_") == self.start_subtask:
                        skip_to_start_subtask = False # Found the starting point, stop skipping
                        if self.verbose: print(f"--- [INFO] Resuming from subtask: {subtask}")
                    else:
                        if self.verbose: print(f"--- [INFO] Skipping subtask: {subtask}")
                        continue # Skip this subtask

                # Original logic for executing subtasks
                if self.verbose: print(f"{'&'*30}\nBeginning subtask: {subtask}\n{'&'*30}")
                if type(self.phase_models) == dict:
                    if subtask in self.phase_models:
                        self.set_model(self.phase_models[subtask])
                    else: self.set_model(f"{DEFAULT_LLM_BACKBONE}")
                
                # Check if subtask needs to be performed
                if (subtask not in self.phase_status or not self.phase_status[subtask]):
                    return_to_exp_phase = False  # Initialize the variable

                    if subtask == "literature review":
                        repeat = True
                        while repeat: repeat = self.literature_review()
                        self.phase_status[subtask] = True

                    elif subtask == "plan formulation":
                        repeat = True
                        while repeat: repeat = self.plan_formulation()
                        self.phase_status[subtask] = True
                    elif subtask == "data preparation":
                        repeat = True
                        while repeat: repeat = self.data_preparation()
                        self.phase_status[subtask] = True
                    elif subtask == "running experiments":
                        repeat = True
                        while repeat: repeat = self.running_experiments()
                        self.phase_status[subtask] = True
                    elif subtask == "results interpretation":
                        repeat = True
                        while repeat: repeat = self.results_interpretation()
                        self.phase_status[subtask] = True
                    elif subtask == "report writing":
                        repeat = True
                        while repeat: repeat = self.report_writing()
                        self.phase_status[subtask] = True
                    elif subtask == "report refinement":
                        return_to_exp_phase = self.report_refinement()

                        if not return_to_exp_phase:
                            if self.save: self.save_state(subtask)
                            return

                    # Only execute the following if we need to return to experiment phase
                    if return_to_exp_phase:
                        self.set_agent_attr("second_round", return_to_exp_phase)
                        self.set_agent_attr("prev_report", copy(self.phd.report))
                        self.set_agent_attr("prev_exp_results", copy(self.phd.exp_results))
                        self.set_agent_attr("prev_results_code", copy(self.phd.results_code))
                        self.set_agent_attr("prev_interpretation", copy(self.phd.interpretation))

                        self.phase_status["plan formulation"] = False
                        self.phase_status["data preparation"] = False
                        self.phase_status["running experiments"] = False
                        self.phase_status["results interpretation"] = False
                        self.phase_status["report writing"] = False
                        self.phase_status["report refinement"] = False
                        self.perform_research()
                if self.save: self.save_state(subtask)
                # Calculate and print the duration of the phase
                phase_end_time = time.time()
                phase_duration = phase_end_time - phase_start_time
                print(f"Subtask '{subtask}' completed in {phase_duration:.2f} seconds.")
                self.statistics_per_phase[subtask]["time"] = phase_duration

        # After all phases, specifically after report refinement, check for --save-docx
        # Ensure this runs outside the subtask loop, after all phases are completed or at the very end of perform_research.
        # The check for 'report refinement' completion ensures we have a final paper to save.
        if hasattr(self, 'args') and self.args and hasattr(self.args, 'save_docx') and self.args.save_docx and self.phase_status.get("report refinement", False):
            print("\nAttempting to save the report as Markdown...")
            # Try to get the final report from different possible sources
            final_paper_text = None
            
            # First try to get from self.phd.report
            if hasattr(self, 'phd') and hasattr(self.phd, 'report') and self.phd.report:
                final_paper_text = self.phd.report
            # Then try from solver if it exists
            elif hasattr(self, 'solver') and self.solver and hasattr(self.solver, 'paper_lines') and self.solver.paper_lines:
                final_paper_text = "\n".join(self.solver.paper_lines)
            
            if final_paper_text:
                # Determine save path
                markdown_save_path = os.path.join("./research_dir", "final_report.md")
                
                # Ensure the directory exists
                if not os.path.exists(os.path.dirname(markdown_save_path)):
                    os.makedirs(os.path.dirname(markdown_save_path), exist_ok=True)
                
                try:
                    from utils import save_text_to_markdown
                    if save_text_to_markdown(final_paper_text, markdown_save_path):
                        print(f"Final report successfully saved to {markdown_save_path}")
                    else:
                        print(f"Failed to save final report to {markdown_save_path}")
                except ImportError:
                    # Fallback to simple file writing if utils is not available
                    try:
                        with open(markdown_save_path, 'w', encoding='utf-8') as f:
                            f.write(final_paper_text)
                        print(f"Final report successfully saved to {markdown_save_path}")
                    except Exception as e:
                        print(f"Failed to save final report: {e}")
            else:
                print("Could not retrieve final paper content to save as Markdown.")


    def report_refinement(self):
        """
        Perform report refinement phase
        @return: (bool) whether to repeat the phase
        """
        reviews = self.reviewers.inference(self.phd.plan, self.phd.report)
        print("Reviews:", reviews)
        if self.human_in_loop_flag["report refinement"]:
            print(f"Provided are reviews from a set of three reviewers: {reviews}")
            input("Would you like to be completed with the project or should the agents go back and improve their experimental results?\n (y) for go back (n) for complete project: ")
        else:
            review_prompt = f"Provided are reviews from a set of three reviewers: {reviews}. Would you like to be completed with the project or do you want to go back to the planning phase and improve your experiments?\n Type y and nothing else to go back, type n and nothing else for complete project."
            self.phd.phases.append("report refinement")
            if self.review_override:
                if self.review_total_steps == self.review_ovrd_steps:
                    response = "n"
                else:
                    response = "y"
                    self.review_ovrd_steps += 1
            else:
                response = self.phd.inference(
                    research_topic=self.research_topic, phase="report refinement", feedback=review_prompt, step=0)
            if len(response) == 0:
                raise Exception("Model did not respond")
            response = response.lower().strip()[0]
            if response == "n":
                if verbose: print("*"*40, "\n", "REVIEW COMPLETE", "\n", "*"*40)
                return False
            elif response == "y":
                self.set_agent_attr("reviewer_response", f"Provided are reviews from a set of three reviewers: {reviews}.")
                return True
            else: raise Exception("Model did not respond")

    def report_writing(self):
        """
        执行报告撰写阶段
        @return: (bool) 是否重复该阶段
        """
        # experiment notes
        report_notes = [_note["note"] for _note in self.ml_engineer.notes if "report writing" in _note["phases"]]
        report_notes = f"Notes for the task objective: {report_notes}\n" if len(report_notes) > 0 else ""
        # instantiate mle-solver
        from papersolver import PaperSolver
        self.reference_papers = []

        # --- 图片处理逻辑（假设）---
        # 假设在实验运行后，图片路径列表存储在 self.phd.generated_figures_list
        # 路径应相对于 ./research_dir/
        # 例如: self.phd.generated_figures_list = ["silicon_analysis_plots_20230101_120000/target_distribution.png", "silicon_analysis_plots_20230101_120000/advanced_correlation_matrix.png"]
        # 在实际应用中，您需要确保此列表被正确填充。
        # 为了演示，如果属性不存在，则使用空列表。
        generated_images_list = getattr(self.phd, 'generated_figures_list', [])
        if not generated_images_list and self.verbose:
            print("⚠️ [报告撰写] 未找到生成的图片列表 (self.phd.generated_figures_list)。图片可能不会被插入。")
        elif generated_images_list and self.verbose:
            print(f"ℹ️ [报告撰写] 将尝试使用以下图片列表插入报告: {generated_images_list}")
        # --- 结束图片处理逻辑 ---

        solver = PaperSolver(
            notes=report_notes,
            max_steps=self.papersolver_max_steps,
            plan=self.phd.plan,
            exp_code=self.phd.results_code,
            exp_results=self.phd.exp_results,
            insights=self.phd.interpretation,
            lit_review=self.phd.lit_review,
            ref_papers=self.reference_papers,
            topic=self.research_topic,
            openai_api_key=self.openai_api_key,
            openai_base_url=self.openai_base_url,
            openai_model_name=self.openai_model_name,
            llm_str=self.model_backbone["report writing"],
            compile_pdf=self.compile_pdf,
            author_name=self.author_name,
            generated_images=generated_images_list, # 传递图片列表
            semantic_references=self.semantic_references # 传递语义搜索参考文献
        )
        # run initialization for solver
        solver.initial_solve()
        # run solver for N mle optimization steps
        for _ in range(self.papersolver_max_steps):
            solver.solve()
        # get best report results
        report = "\n".join(solver.best_report[0][0])
        score = solver.best_report[0][1]
        
        # New logic to insert images into the report
        if generated_images_list:
            image_tags = []
            for i, img_path in enumerate(generated_images_list):
                # Generate a simple alt text; more advanced could involve LLM or file name parsing
                alt_text = f"图 {i+1}: 分析结果 ({os.path.basename(img_path)})"
                # Typora requires relative paths to be relative to the markdown file
                # The markdown file is in research_dir, so the path should be relative to that
                relative_path = os.path.relpath(img_path, 'agentlaboratory623/research_dir')
                markdown_tag = f"![{alt_text}]({relative_path})"
                image_tags.append(markdown_tag)
            
            # Append images to the end of the report in a new section
            # Consider a more intelligent insertion point if available, e.g., a placeholder in the report template
            report += "\n\n## 实验结果图示\n\n" + "\n\n".join(image_tags)
            if self.verbose:
                print(f"✅ [报告撰写] 成功将 {len(generated_images_list)} 张图片插入报告。")

        if self.verbose: print(f"Report writing completed, reward function score: {score}")
        if self.human_in_loop_flag["report writing"]:
            retry = self.human_in_loop("report writing", report)
            if retry: return retry
        self.set_agent_attr("report", report)
        readme = self.professor.generate_readme()
        save_to_file("./research_dir", "readme.md", readme)
        save_to_file("./research_dir", "report.txt", report)
        self.reset_agents()
        return False

    def results_interpretation(self):
        """
        执行结果解释阶段
        @return: (bool) 是否重复该阶段
        """
        max_tries = self.max_steps
        dialogue = str()
        # iterate until max num tries to complete task is exhausted
        for _i in range(max_tries):
            print(f"🔬 [Step {_i+1}] Postdoc analyzing results...")
            resp = self.postdoc.inference(self.research_topic, "results interpretation", feedback=dialogue, step=_i)
            print("="*80)
            print("🧑‍🔬 POSTDOC RESPONSE:")
            print("="*80)
            print(resp)
            print("="*80)
            dialogue = str()
            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the postdoctoral researcher: {dialogue}"
                print("#"*40)
                print("📝 POSTDOC DIALOGUE EXTRACTED:")
                print("#"*40)
                print(dialogue)
                print("#"*40)
                # Save dialogue
                self.save_dialogue("Postdoc", dialogue, "results interpretation")
            if "```INTERPRETATION" in resp:
                interpretation = extract_prompt(resp, "INTERPRETATION")
                if self.human_in_loop_flag["results interpretation"]:
                    retry = self.human_in_loop("results interpretation", interpretation)
                    if retry: return retry
                self.set_agent_attr("interpretation", interpretation)
                # reset agent state
                self.reset_agents()
                self.statistics_per_phase["results interpretation"]["steps"] = _i
                return False
            print(f"🎓 [Step {_i+1}] PhD Student responding...")
            resp = self.phd.inference(self.research_topic, "results interpretation", feedback=dialogue, step=_i)
            print("="*80)
            print("🎓 PHD STUDENT RESPONSE:")
            print("="*80)
            print(resp)
            print("="*80)
            dialogue = str()
            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the PhD student: {dialogue}"
                print("#"*40)
                print("📝 PHD DIALOGUE EXTRACTED:")
                print("#"*40)
                print(dialogue)
                print("#"*40)
                # Save dialogue
                self.save_dialogue("PhD", dialogue, "results interpretation")
        raise Exception("Max tries during phase: Results Interpretation")

    def running_experiments(self):
        """
        Perform running experiments phase by executing the visualization script as a subprocess.
        @return: (bool) whether to repeat the phase
        """
        import subprocess
        import sys

        # experiment notes
        experiment_notes = [_note["note"] for _note in self.ml_engineer.notes if "running experiments" in _note["phases"]]
        experiment_notes = f"Notes for the task objective: {experiment_notes}\n" if len(experiment_notes) > 0 else ""

        try:
            script_path = 'run_analysis.py'
            
            # Ensure the script exists before trying to run it
            if not os.path.exists(script_path):
                 raise FileNotFoundError(f"Error: Script '{script_path}' not found.")

            if self.verbose: print(f"Executing script as a subprocess: {script_path}")
            
            # Execute the script as a separate process to ensure __name__ == "__main__"
            # Using sys.executable ensures we use the same Python interpreter
            process = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=600, # 10-minute timeout
                encoding='utf-8'
            )

            # Print stdout and stderr for debugging purposes
            print("="*40, "SUBPROCESS STDOUT", "="*40)
            print(process.stdout)
            print("="*40, "SUBPROCESS STDERR", "="*40)
            if process.stderr:
                print(process.stderr)
            print("="*90)

            if process.returncode != 0:
                raise Exception(f"Script execution failed with return code {process.returncode}.\nStderr: {process.stderr}")

            code_resp = process.stdout
            
            # --- New JSON Parsing Logic ---
            print("ATTEMPTING TO PARSE JSON RESULTS FROM SCRIPT OUTPUT")
            exp_results = "Script executed, but no structured JSON results were found. Check logs for details."
            json_results = None
            
            try:
                json_match = re.search(r"<AGENT_LAB_JSON_RESULTS>(.*?)</AGENT_LAB_JSON_RESULTS>", code_resp, re.DOTALL)
                if json_match:
                    json_string = json_match.group(1)
                    json_results = json.loads(json_string)
                    exp_results = json.dumps(json_results, indent=2)
                    print("✅ Successfully parsed JSON results from script output.")
                else:
                    print("⚠️ Warning: Could not find <AGENT_LAB_JSON_RESULTS> markers in the script output.")
            except json.JSONDecodeError as e:
                print(f"❌ Error: Failed to decode JSON from script output. Error: {e}")

            # --- Image Collection Logic (using parsed JSON) ---
            if json_results and json_results.get("all_plot_paths"):
                self.phd.generated_figures_list = list(json_results["all_plot_paths"].values())
                if self.verbose:
                    print(f"✅ [图片扫描] 成功从JSON结果中收集到以下图片路径: {self.phd.generated_figures_list}")
            else:
                 if self.verbose: print("⚠️ [图片扫描] 未能在JSON结果中找到图片路径。")

            score = 1.0 if json_results else 0.5
            # Save the original script content to results_code
            with open(script_path, 'r', encoding='utf-8') as f:
                code = f.read()

        except (FileNotFoundError, Exception) as e:
            error_msg = f"Error during script execution: {e}"
            print(error_msg)
            exp_results = f"Script execution failed: {e}"
            score = 0.0
            code = f"# Failed to execute script: {script_path}"

        if self.human_in_loop_flag["running experiments"]:
            retry = self.human_in_loop("running experiments", exp_results)
            if retry: return retry
        
        save_to_file("./research_dir/src", "run_experiments.py", code)
        self.set_agent_attr("results_code", code)
        self.set_agent_attr("exp_results", exp_results)
        # reset agent state
        self.reset_agents()
        return False

    def data_preparation(self):
        """
        Data preparation phase with enhanced retry mechanism for empty code blocks
        """
        if self.verbose: print("^"*50, "data preparation", "^"*50)
        
        # PhD Student response
        phd_context = self.phd.context("data preparation")
        if isinstance(phd_context, tuple):
            phd_context = "".join(phd_context)
        phd_resp = self.phd.inference(self.research_topic, "data preparation", 0,
                                        phd_context +
                                        self.phd.phase_prompt("data preparation"))
        phd_dialogue = ""
        if phd_resp is not None:
            phd_dialogue = f"\nThe following is dialogue produced by the PhD Student: {phd_resp}\n"
            if self.verbose: print("#" * 40, f"\nThe following is dialogue produced by the PhD Student: {phd_resp}", "#" * 40, "\n")
            # Save dialogue
            self.save_dialogue("PhD", phd_resp, "data preparation")
        
        # ML Engineer response
        ml_context = self.ml_engineer.context("data preparation")
        if isinstance(ml_context, tuple):
            ml_context = "".join(ml_context)
        ml_resp = self.ml_engineer.inference(self.research_topic, "data preparation", 0,
                                            ml_context +
                                            self.ml_engineer.phase_prompt("data preparation"))
        ml_dialogue = ""
        ml_feedback = ""
        ml_command = ""
        
        if ml_resp is not None:
            ml_dialogue = f"\nThe following is dialogue produced by the ML Engineer: {ml_resp}\n"
            if self.verbose: print("#" * 40, f"\nThe following is dialogue produced by the ML Engineer: {ml_resp}", "#" * 40, "\n")
            # Save dialogue
            self.save_dialogue("ML Engineer", ml_resp, "data preparation")
        
        # Extract and execute code
        if ml_resp and "```python" in ml_resp:
            code = extract_prompt(ml_resp, "python")
            if code and code.strip():  # Check if code is not empty
                # Safely handle dataset_code concatenation
                dataset_code = self.ml_engineer.dataset_code or ""
                full_code = dataset_code + "\n" + code if dataset_code else code
                
                code_resp = execute_code(full_code, timeout=120)
                ml_command = f"Code produced by the ML agent:\n{full_code}"
                ml_feedback += f"\nCode Response: {code_resp}\n"
                if self.verbose: print("!"*100, "\n", f"CODE RESPONSE: {code_resp}")
            else:
                if self.verbose: print("Warning: SUBMIT_CODE was found but the content was empty. Cannot execute empty code.")
                ml_feedback += "\n[CODE EXECUTION ERROR] SUBMIT_CODE was empty.\n"
                if self.verbose: print("!"*100, "\n", f"CODE RESPONSE: [CODE EXECUTION ERROR] SUBMIT_CODE was empty.")
        
        # Handle HuggingFace search
        if ml_resp and "```SEARCH_HF" in ml_resp:
            hf_query = extract_prompt(ml_resp, "SEARCH_HF")
            if hf_query and hf_query.strip():
                hf_resp = search_huggingface(hf_query)
                ml_feedback += f"\nHuggingFace Search Response: {hf_resp}\n"
                if self.verbose: print("HuggingFace Search Response:", hf_resp)
            else:
                if self.verbose: print("Warning: Could not extract SEARCH_HF query or query was empty from ML Engineer response.")

        # Update cost tracking
        self.cost += self.phd.cost + self.ml_engineer.cost
        if self.verbose: print(f"Current experiment cost = ${self.cost}, ** Approximate values, may not reflect true cost")

        # Update communication history
        self.phd.prev_comm += phd_dialogue + ml_dialogue + ml_command + ml_feedback
        self.ml_engineer.prev_comm += phd_dialogue + ml_dialogue + ml_command + ml_feedback

        # Save state
        self.save_state("data preparation")
        
        # Continue to next phase if we have any progress, even with errors
        return False

    def plan_formulation(self):
        """
        Perform plan formulation phase
        @return: (bool) whether to repeat the phase
        """
        max_tries = self.max_steps
        dialogue = str()
        # iterate until max num tries to complete task is exhausted
        for _i in range(max_tries):
            # inference postdoc to
            resp = self.postdoc.inference(self.research_topic, "plan formulation", feedback=dialogue, step=_i)
            if self.verbose: print("Postdoc: ", resp, "\n~~~~~~~~~~~")
            dialogue = str()

            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the postdoctoral researcher: {dialogue}"
                if self.verbose: print("#"*40, "\n", "Postdoc Dialogue:", dialogue, "\n", "#"*40)
                # Save dialogue
                self.save_dialogue("Postdoc", dialogue, "plan formulation")

            if "```PLAN" in resp:
                plan = extract_prompt(resp, "PLAN")
                if self.human_in_loop_flag["plan formulation"]:
                    retry = self.human_in_loop("plan formulation", plan)
                    if retry: return retry
                self.set_agent_attr("plan", plan)
                # reset agent state
                self.reset_agents()
                self.statistics_per_phase["plan formulation"]["steps"] = _i
                return False

            resp = self.phd.inference(self.research_topic, "plan formulation", feedback=dialogue, step=_i)
            if self.verbose: print("PhD Student: ", resp, "\n~~~~~~~~~~~")

            dialogue = str()
            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the PhD student: {dialogue}"
                if self.verbose: print("#"*40, "\n", "PhD Dialogue:", dialogue, "#"*40, "\n")
                # Save dialogue
                self.save_dialogue("PhD", dialogue, "plan formulation")
        raise Exception("Max tries during phase: Plan Formulation")

    def literature_review(self):
        """
        Perform literature review phase
        @return: (bool) whether to repeat the phase
        """
        # If num_papers_lit_review is 0, skip this phase entirely
        if self.arxiv_num_summaries == 0:
            print("Skipping literature review phase as num_papers_lit_review is 0.")
            return False # Do not repeat phase

        # Initialize search engines
        arx_eng = ArxivSearch()
        perplexity_eng = None
        semantic_eng = None

        # Initialize Semantic Scholar search
        try:
            from enhanced_semantic_search import SemanticScholarSearch
            # Try to get API key from environment or use None for public API
            semantic_api_key = os.getenv('SEMANTIC_SCHOLAR_API_KEY')
            # Use custom API if key is available
            semantic_eng = SemanticScholarSearch(api_key=semantic_api_key, use_custom_api=True)
            print("✅ Semantic Scholar search initialized")
        except ImportError as e:
            print(f"❌ Semantic Scholar search not available: {e}")
            semantic_eng = None

        # Try to initialize Perplexity with optimal configuration
        perplexity_config = None
        try:
            from perplexity_search import PerplexitySearch
            from perplexity_config import setup_perplexity_for_agent_lab

            perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
            if perplexity_api_key:
                # Setup optimal configuration
                perplexity_config = setup_perplexity_for_agent_lab(self.research_topic, budget=15.0)
                if "error" not in perplexity_config:
                    perplexity_eng = PerplexitySearch(perplexity_api_key)
                    print("✅ Perplexity AI initialized as PRIMARY literature review engine")
                    print("📚 Enhanced with optimal configuration for your research topic")
                    print(f"🎯 Strategy: {perplexity_config['strategy']['primary_approach']}")
                    print(f"💰 Estimated cost: ${perplexity_config['strategy']['estimated_cost']:.3f}")
                else:
                    print(f"❌ Perplexity configuration error: {perplexity_config['error']}")
                    perplexity_eng = None
            else:
                print("⚠️  PERPLEXITY_API_KEY not found. Using ArXiv search only.")
                print("💡 For enhanced literature review, set PERPLEXITY_API_KEY environment variable.")
                perplexity_eng = None
        except ImportError as e:
            print("❌ Perplexity search not available. Using ArXiv search only.")
            print(f"💡 Install perplexity dependencies: {e}")
            perplexity_eng = None

        max_tries = self.max_steps * 5 # lit review often requires extra steps

        # Provide initial guidance based on available tools
        initial_guidance = ""
        available_engines = []
        if perplexity_eng:
            available_engines.append("PERPLEXITY_REVIEW")
        if semantic_eng:
            available_engines.append("SEMANTIC_SEARCH")
        available_engines.append("SUMMARY (ArXiv)")

        if perplexity_eng and semantic_eng:
            initial_guidance = (
                "🚀 MULTIPLE SEARCH ENGINES AVAILABLE:\n"
                "1. PERPLEXITY_REVIEW - AI-powered comprehensive analysis\n"
                "2. SEMANTIC_SEARCH - Academic paper search via Semantic Scholar\n"
                "3. SUMMARY - Traditional ArXiv search\n"
                "RECOMMENDED: Start with SEMANTIC_SEARCH for targeted academic papers, then use PERPLEXITY_REVIEW for broader analysis."
            )
        elif semantic_eng:
            initial_guidance = (
                "📚 SEMANTIC_SEARCH and ArXiv search available. "
                "Use SEMANTIC_SEARCH for comprehensive academic paper discovery from Semantic Scholar database."
            )
        elif perplexity_eng:
            initial_guidance = (
                "🚀 RECOMMENDED: Start with PERPLEXITY_REVIEW command for comprehensive literature analysis. "
                "Perplexity AI provides access to multiple academic databases with AI-powered insights and automatic citations."
            )
        else:
            initial_guidance = (
                "📚 Using ArXiv search for literature review. "
                "For enhanced results, consider setting up additional search engines."
            )

        # get initial response from PhD agent
        resp = self.phd.inference(self.research_topic, "literature review", feedback=initial_guidance, step=0, temp=0.8)
        if self.verbose: print(resp, "\n~~~~~~~~~~~")
        # iterate until max num tries to complete task is exhausted
        for _i in range(max_tries):
            feedback = str()

            # grab summary of papers from arxiv and perplexity
            if "```SUMMARY" in resp:
                query = extract_prompt(resp, "SUMMARY")
                if query:
                    # Use ArXiv search
                    try:
                        papers = arx_eng.find_papers_by_str(query, N=self.arxiv_num_summaries)
                        
                        # Save the search results to a markdown file for each query
                        # Sanitize the query to create a valid filename, allowing Chinese characters
                        filename_query = re.sub(r'[^\w\s\-\.\u4e00-\u9fa5]', '', query.strip()).replace(' ', '_')
                        
                        # Fallback for empty or invalid filenames
                        if not filename_query:
                            filename_query = "search_results"

                        import datetime
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"SUMMARY_{filename_query}_{timestamp}.md"
                        filepath = os.path.join("./research_dir", filename)
                        papers_str = papers if isinstance(papers, str) else str(papers)
                        
                        # Corrected argument order for save_text_to_markdown
                        if save_text_to_markdown(papers_str, filepath):
                            if self.verbose:
                                print(f"✅ Search results for query '{query.strip()}' saved to: {filepath}")
                        # The save_text_to_markdown function will print its own error message on failure.

                    except Exception as e:
                        print(f"Warning: ArXiv search failed due to a network error: {e}")
                        print("Skipping this search query and continuing.")
                        papers = f"ArXiv search failed for query: {query}. Please try a different query."
                else:
                    print("Warning: Could not extract SUMMARY query from LLM response for literature review.")
                    papers = "Could not retrieve papers as the SUMMARY query was empty." # Provide some feedback
                feedback = f"You requested papers related to the query '{query}', here was the response\n{papers}"

            # grab full text from arxiv ID
            elif "```FULL_TEXT" in resp:
                query = extract_prompt(resp, "FULL_TEXT")
                # expiration timer so that paper does not remain in context too long
                arxiv_paper = f"```EXPIRATION {self.arxiv_paper_exp_time}\n" + arx_eng.retrieve_full_paper_text(query) + "```"
                feedback = arxiv_paper

            # if add paper, extract and add to lit review, provide feedback
            elif "```ADD_PAPER" in resp:
                query = extract_prompt(resp, "ADD_PAPER")
                feedback, text = self.phd.add_review(query, arx_eng)
                if len(self.reference_papers) < self.num_ref_papers:
                    self.reference_papers.append(text)

            # Enhanced Semantic Scholar search
            elif "```SEMANTIC_SEARCH" in resp and semantic_eng:
                query = extract_prompt(resp, "SEMANTIC_SEARCH")
                if query:
                    try:
                        print(f"🔍 Performing Semantic Scholar search for: {query}")
                        review_text, references = semantic_eng.generate_literature_review(query, num_papers=self.arxiv_num_summaries)

                        # Store references for later use
                        self.semantic_references.extend(references)

                        # Save the review to a markdown file
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename_query = re.sub(r'[^\w\s\-\.\u4e00-\u9fa5]', '', query.strip()).replace(' ', '_')
                        if not filename_query:
                            filename_query = "semantic_search"

                        filename = f"SEMANTIC_{filename_query}_{timestamp}.md"
                        filepath = os.path.join("./research_dir", filename)

                        if save_text_to_markdown(review_text, filepath):
                            print(f"✅ Semantic Scholar review saved to: {filepath}")

                        feedback = f"Semantic Scholar search completed for '{query}'. Found {len(references)} papers. Review saved to {filename}.\n\n{review_text[:1000]}..."

                    except Exception as e:
                        print(f"❌ Semantic Scholar search failed: {e}")
                        feedback = f"Semantic Scholar search failed for query: {query}. Error: {str(e)}"
                else:
                    feedback = "❌ Could not extract SEMANTIC_SEARCH query from response."

            # Fallback message if Semantic Scholar is not available
            elif "```SEMANTIC_SEARCH" in resp and not semantic_eng:
                feedback = "❌ Semantic Scholar search is not available. Please use SUMMARY command for ArXiv search."

            # Enhanced Perplexity literature review generation - PRIMARY METHOD
            elif "```PERPLEXITY_REVIEW" in resp and perplexity_eng:
                feedback = "Perplexity has been disabled. Please use the 'SUMMARY' command to search on ArXiv."

            # Fallback message if Perplexity is not available
            elif "```PERPLEXITY_REVIEW" in resp and not perplexity_eng:
                feedback = "❌ Perplexity AI is not available. Please use SUMMARY command for ArXiv search or set up Perplexity API key."

            # completion condition
            if len(self.phd.lit_review) >= self.num_papers_lit_review:
                # generate formal review
                lit_review_sum = self.phd.format_review()

                # Save the literature review to a markdown file for review
                # Save the literature review to a markdown file for review, only if it's not empty
                if lit_review_sum and lit_review_sum.strip():
                    review_filepath = os.path.join("./research_dir", "literature_review_summary.md")
                    if save_text_to_markdown(lit_review_sum, review_filepath):
                        if self.verbose:
                            print(f"✅ Literature review summary saved to: {review_filepath}")
                    # The save_text_to_markdown function will print its own error on failure.
                else:
                    if self.verbose:
                        print("⚠️  Literature review summary was empty. Skipping save.")
                # if human in loop -> check if human is happy with the produced review
                if self.human_in_loop_flag["literature review"]:
                    retry = self.human_in_loop("literature review", lit_review_sum)
                    # if not happy, repeat the process with human feedback
                    if retry:
                        self.phd.lit_review = []
                        return retry
                # otherwise, return lit review and move on to next stage
                if self.verbose: print(self.phd.lit_review_sum)
                # set agent
                self.set_agent_attr("lit_review_sum", lit_review_sum)
                # reset agent state
                self.reset_agents()
                self.statistics_per_phase["literature review"]["steps"] = _i
                return False
            resp = self.phd.inference(self.research_topic, "literature review", feedback=feedback, step=_i + 1, temp=0.8)
            if self.verbose: print(resp, "\n~~~~~~~~~~~")
        raise Exception("Max tries during phase: Literature Review")

    def human_in_loop(self, phase, phase_prod):
        """
        Get human feedback for phase output
        @param phase: (str) current phase
        @param phase_prod: (str) current phase result
        @return: (bool) whether to repeat the loop
        """
        print("\n\n\n\n\n")
        print(f"Presented is the result of the phase [{phase}]: {phase_prod}")
        y_or_no = None
        # repeat until a valid answer is provided
        while y_or_no not in ["y", "n"]:
            y_or_no = input("\n\n\nAre you happy with the presented content? Respond Y or N: ").strip().lower()
            # if person is happy with feedback, move on to next stage
            if y_or_no == "y": pass
            # if not ask for feedback and repeat
            elif y_or_no == "n":
                # ask the human for feedback
                notes_for_agent = input("Please provide notes for the agent so that they can try again and improve performance: ")
                # reset agent state
                self.reset_agents()
                # add suggestions to the notes
                self.notes.append({
                    "phases": [phase],
                    "note": notes_for_agent})
                return True
            else: print("Invalid response, type Y or N")
        return False



def parse_arguments():
    parser = argparse.ArgumentParser(description="AgentLaboratory Research Workflow")


    parser.add_argument(
        '--copilot-mode',
        type=str,
        default="True",
        help='Enable human interaction mode.'
    )

    parser.add_argument(
        '--load-existing',
        type=str,
        default="False",
        help='Do not load existing state; start a new workflow.'
    )

    parser.add_argument(
        '--load-existing-path',
        type=str,
        help='Path to load existing state; start a new workflow, e.g. state_saves/results_interpretation.pkl'
    )

    parser.add_argument(
        '--research-topic',
        type=str,
        help='Specify the research topic.'
    )

    parser.add_argument(
        '--api-key',
        type=str,
        help='Provide the OpenAI API key.'
    )

    parser.add_argument(
        '--openai-base-url',
        type=str,
        help='Provide the base URL for OpenAI compatible API (e.g., for local LLMs).'
    )

    parser.add_argument(
        '--openai-model-name',
        type=str,
        default="gpt-4o",
        help='Specify the model name to use with OpenAI compatible API.'
    )

    parser.add_argument(
        '--compile-latex',
        type=str,
        default="True",
        help='Compile latex into pdf during paper writing phase. Disable if you can not install pdflatex.'
    )

    parser.add_argument(
        '--llm-backend',
        type=str,
        default="gemini-2.5-flash",
        help='Backend LLM to use for agents in Agent Laboratory.'
    )

    parser.add_argument(
        '--language',
        type=str,
        default="English",
        help='Language to operate Agent Laboratory in.'
    )

    parser.add_argument(
        '--num-papers-lit-review',
        type=str,
        default="5",
        help='Total number of papers to summarize in literature review stage'
    )

    parser.add_argument(
        '--mlesolver-max-steps',
        type=str,
        default="2",
        help='Total number of mle-solver steps'
    )

    parser.add_argument(
        '--papersolver-max-steps',
        type=str,
        default="2",
        help='Total number of paper-solver steps'
    )

    parser.add_argument(
        '--perplexity-api-key',
        type=str,
        help='Perplexity API key for enhanced literature review'
    )

    parser.add_argument(
        '--use-perplexity',
        type=str,
        default="True",
        help='Enable Perplexity for literature review (True/False)'
    )

    parser.add_argument(
        '--literature-engine',
        type=str,
        default="both",
        choices=["arxiv", "perplexity", "both"],
        help='Choose literature search engine: arxiv, perplexity, or both'
    )

    parser.add_argument(
        '--save-docx',
        action="store_true",
        help='Save the final report as a Markdown file instead of compiling PDF if LaTeX is not available.'
    )

    parser.add_argument(
        '--author-name',
        type=str,
        default="Xiao",
        help='Specify the author name for the report.'
    )

    parser.add_argument(
        '--semantic-api-key',
        type=str,
        default=None,
        help='Semantic Scholar API key for enhanced literature search'
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_arguments()

    llm_backend = args.llm_backend
    human_mode = args.copilot_mode.lower() == "true"
    compile_pdf = args.compile_latex.lower() == "true"
    load_existing = args.load_existing.lower() == "true"
    try:
        num_papers_lit_review = int(args.num_papers_lit_review.lower())
    except Exception:
        raise Exception("args.num_papers_lit_review must be a valid integer!")
    try:
        papersolver_max_steps = int(args.papersolver_max_steps.lower())
    except Exception:
        raise Exception("args.papersolver_max_steps must be a valid integer!")
    try:
        mlesolver_max_steps = int(args.mlesolver_max_steps.lower())
    except Exception:
        raise Exception("args.papersolver_max_steps must be a valid integer!")


    api_key = os.getenv('OPENAI_API_KEY') or args.api_key or "your-default-api-key"
    if not api_key:
        raise ValueError("API key must be provided via --api-key or the OPENAI_API_KEY environment variable.")

    # Handle OpenAI compatible model parameters
    openai_base_url = args.openai_base_url
    openai_model_name = args.openai_model_name

    # Handle Perplexity API key
    perplexity_api_key = args.perplexity_api_key or os.getenv('PERPLEXITY_API_KEY')
    if perplexity_api_key:
        os.environ['PERPLEXITY_API_KEY'] = perplexity_api_key
        print("Perplexity API key configured for enhanced literature review.")
    else:
        print("No Perplexity API key provided. Literature review will use ArXiv only.")

    # Handle Semantic Scholar API key
    if args.semantic_api_key:
        os.environ['SEMANTIC_SCHOLAR_API_KEY'] = args.semantic_api_key
        print("Semantic Scholar API key configured for enhanced literature search.")
    elif os.getenv('SEMANTIC_SCHOLAR_API_KEY'):
        print("Semantic Scholar API key found in environment variables.")
    else:
        print("No Semantic Scholar API key provided. Using public API with rate limits.")

    use_perplexity = args.use_perplexity.lower() == "true"
    literature_engine = args.literature_engine
    author_name = args.author_name # Get author_name from args

    ##########################################################
    # Research question that the agents are going to explore #
    ##########################################################
    if args.research_topic:
        research_topic = args.research_topic
    else:
        research_topic = input("Please name an experiment idea for AgentLaboratory to perform: ")

    task_notes_LLM = [
        {
            "phases": ["literature review"],
            "note": "For the literature review on '机器学习在电解铝电流效率预测的应用', focus on papers that discussed the application of machine learning, shap and pdp explanation."
        },

        {"phases": ["plan formulation"],
         "note": f"You should come up with a plan for TWO experiments."},

        {"phases": ["data preparation"],
         "note": """
        数据准备阶段将由 `/Users/<USER>/Documents/Python/agentlaboratory/visualization_utils.py` 脚本内部处理。
        此阶段不需要生成额外的数据准备代码。
        请确认数据文件 `/Users/<USER>/Documents/Python/agentlaboratory/data/Si2025_5_4.csv` 可用。
        """},

        {"phases": ["running experiments"],
      "note": """
        请直接执行位于 `/Users/<USER>/Documents/Python/agentlaboratory/visualization_utils.py` 的现有机器学习分析脚本。
        这个脚本已经包含了所有必要的数据预处理、模型训练（包括 Stacking 集成模型）、评估和可解释性分析（SHAP 和 PDP）。
        执行此脚本后，请确保捕获其输出（特别是模型性能指标和生成的图片路径），并将其作为实验结果进行传递，以便后续报告撰写和解释。
        不需要生成新的代码。
        """},
        {"phases": ["data preparation", "running experiments"],
         "note": """
         构建Stacking集成学习模型，包含完整的超参数优化：

            def build_stacking_model(X_train, X_test, y_train, y_test, preprocessor, output_folder):
            1. KNN: n_neighbors=[5,10,15,20], weights=['uniform','distance'], p=[1,2]
            2. RandomForest: n_estimators=[100,200], max_depth=[None,10,20]
            3. XGBoost: n_estimators=[100,200], max_depth=[3,6,10], learning_rate=[0.01,0.1]
            4. LightGBM: 同XGBoost参数 + subsample=[0.8,1.0]
            5. CatBoost: iterations=[100,200], depth=[3,6], learning_rate=[0.01,0.1]
            6. MLP: hidden_layer_sizes=[(50,),(100,),(50,50)], activation=['relu','tanh']

            实现要求：
            - 每个基学习器使用GridSearchCV优化（cv=5）
            - StackingRegressor(cv=5, final_estimator=LinearRegression())
            - 输出个体模型和集成模型的R²对比图
            - 使用viridis配色，包含数值标签"""},

        {"phases": ["data preparation", "running experiments"],
         "note":"""创建模块化的主函数结构：
                def main():
                1. 配置设置（数据路径、目标列名）
                2. 创建输出文件夹
                3. 数据加载与基本信息展示
                4. EDA分析（分布图、高级相关性矩阵、特征交互）
                5. 数据预处理
                6. LazyPredict模型快速评估
                7. XGBoost单模型训练评估
                8. Stacking集成模型构建
                9. 多层次SHAP分析
                10. PDP分析
                11. 最终总结报告

                错误处理：每个步骤包含try-except错误处理
                进度提示：使用print()提供清晰的执行进度信息"""},

        {"phases": ["data preparation", "running experiments"],
         "note": """创建专业的输出文件管理系统：

                def create_output_folder():
                    "时间戳文件夹创建"
                功能要求：
                1. 生成格式：f"silicon_analysis_plots_{timestamp}"
                2. 时间戳格式："%Y%m%d_%H%M%S"
                3. 自动创建文件夹，避免覆盖
                4. 返回文件夹路径供后续函数使用

                文件命名规范：
                - target_distribution.png
                - advanced_correlation_matrix.png
                - feature_interaction_analysis.png
                - stacking_model_comparison.png
                - stacking_base_models_shap.png
                - stacking_meta_learner_shap.png
                - stacking_partial_dependence_plots.png"""},

        {"phases": ["data preparation", "running experiments"],
         "note": """
         创建部分依赖图分析功能：

                def create_pdp_analysis(stacking_model, X_test_processed, feature_names, output_folder):
                    "PDP边际效应分析"

                实现要求：
                1. 检查sklearn.inspection.PartialDependenceDisplay可用性
                2. 选择前6个重要特征进行PDP分析
                3. 使用PartialDependenceDisplay.from_estimator()
                4. 15x10英寸图片，标题"Stacking Model Partial Dependence Plot Analysis"
                5. 包含异常处理和友好错误信息

                应用价值说明：
                - 解释单个特征对预测结果的边际效应
                - 控制其他变量不变的情况下观察特征影响
                - 识别特征与目标变量的非线性关系"""},


        {"phases": ["data preparation", "running experiments"],
         "note": """
         实现多层次SHAP可解释性分析：

                def explain_stacking_model_with_shap(stacking_model, X_train_processed, X_test_processed, feature_names, output_folder):
                  #Stacking模型SHAP分析

                分析层次：
                1. 基学习器SHAP分析：
                - 树模型：TreeExplainer
                - 其他模型：KernelExplainer
                - 2x3子图布局展示各模型SHAP summary
                
                2. 特征重要性对比：
                - 计算各基学习器的平均|SHAP值|
                - 2x3子图展示每个模型的特征重要性排序
                
                3. 元学习器SHAP分析：
                - LinearExplainer分析基学习器贡献度
                - 条形图显示基学习器重要性
                - 包含数值标签和网格线

                错误处理：在SHAP分析失败时显示友好错误信息"""},



        {"phases": ["report writing"],
         "note": f"请在markdown文件上直接替换修改，请将相应等生成等图片插入到文档中，请确保最终生成的论文报告详细、深入，目标长度在4500字左右。"},

        {"phases": ["report refinement"],
        "note": f"请在markdown文件上直接替换修改"},

    ]

    task_notes_LLM.append(
        {"phases": ["literature review", "plan formulation", "data preparation", "running experiments", "results interpretation", "report writing", "report refinement"],
        "note": f"You should always write in the following language to converse and to write the report {args.language}"},
    )

    ####################################################
    ###  Stages where human input will be requested  ###
    ####################################################
    human_in_loop = {
        "literature review":      human_mode,
        "plan formulation":       human_mode,
        "data preparation":       human_mode,
        "running experiments":    human_mode,
        "results interpretation": human_mode,
        "report writing":         human_mode,
        "report refinement":      human_mode,
    }

    ###################################################
    ###  LLM Backend used for the different phases  ###
    ###################################################
    agent_models = {
        "literature review":      llm_backend,
        "plan formulation":       llm_backend,
        "data preparation":       llm_backend,
        "running experiments":    llm_backend,
        "report writing":         llm_backend,
        "results interpretation": llm_backend,
        "paper refinement":       llm_backend,
    }

    if llm_backend not in [ "gemini-1.5-flash", "gemini-2.5-flash", "gemini-2.5-flash-preview-04-17", "gemini-flash", "gemini-flash-2.5", "gemini-2.5-pro"]:
        raise ValueError(f"Invalid llm_backend: {llm_backend}")

    if load_existing:
        load_path = args.load_existing_path
        if load_path is None:
            raise ValueError("Please provide path to load existing state.")
        if "save_states" in load_path:
            load_path = load_path.replace("save_states", "state_saves")
        with open(load_path, "rb") as f:
            lab = pickle.load(f)

            # Add missing attributes for backward compatibility
            if not hasattr(lab, 'semantic_references'):
                lab.semantic_references = list()
                print("--- [INFO] Added semantic_references attribute for backward compatibility")

            # Force update API configurations from command line arguments after loading state
            if args.api_key:
                lab.openai_api_key = api_key
                lab.set_agent_attr("openai_api_key", api_key)
                print(f"--- [INFO] API key updated to: {'****' + api_key[-4:]}")
            if args.openai_base_url:
                lab.openai_base_url = openai_base_url
                lab.set_agent_attr("openai_base_url", openai_base_url)
                print(f"--- [INFO] OpenAI base URL updated to: {openai_base_url}")
            if args.openai_model_name:
                lab.openai_model_name = openai_model_name
                lab.set_agent_attr("openai_model_name", openai_model_name)
                print(f"--- [INFO] OpenAI model name updated to: {openai_model_name}")
            
            # Ensure author_name is set for loaded instances
            if hasattr(args, 'author_name') and args.author_name:
                lab.author_name = args.author_name
                print(f"--- [INFO] Author name for loaded instance updated to: {args.author_name}")
            elif not hasattr(lab, 'author_name'):
                # If the loaded object doesn't have author_name and no command line arg was provided for it
                # set to default. The args.author_name from parse_arguments() already has a default.
                lab.author_name = args.author_name # Use the default from args or the one provided
                print(f"--- [INFO] Author name for loaded instance set to: {lab.author_name}")
            
            # Force update human_in_loop_flag based on command line arguments after loading state
            lab.human_in_loop_flag = human_in_loop # human_in_loop is derived from args.copilot_mode
            if lab.verbose: print(f"--- [INFO] human_in_loop_flag updated to: {lab.human_in_loop_flag}")

            # Reset phases from the loaded state based on --load-existing-path
            # Extract the phase name from the load_existing_path
            loaded_phase_name = os.path.basename(load_path).replace(".pkl", "")
            
            # Find the index of the loaded phase in the phases list
            phase_found = False
            for phase_group_idx, (phase, subtasks) in enumerate(lab.phases):
                for subtask in subtasks:
                    if subtask.replace(" ", "_") == loaded_phase_name:
                        phase_found = True
                        break
                if phase_found:
                    break
            
            if phase_found:
                # Set the loaded phase and all subsequent phases to False
                reset_from_this_point = False
                found_loaded_phase = False
                for phase, subtasks in lab.phases:
                    for subtask in subtasks:
                        if subtask.replace(" ", "_") == loaded_phase_name:
                            found_loaded_phase = True
                            # Don't reset the loaded phase itself, start from the next one
                            continue
                        if found_loaded_phase:
                            reset_from_this_point = True
                        if reset_from_this_point:
                            lab.phase_status[subtask] = False
                            if lab.verbose: print(f"--- [INFO] Resetting phase status for '{subtask}' to False to restart from after '{loaded_phase_name}'")
            else:
                if lab.verbose: print(f"--- [WARNING] Loaded phase '{loaded_phase_name}' not found in defined phases. No phase status reset performed.")
            
            print(f"--- [DEBUG] Phase status after loading and resetting: {lab.phase_status}") # Added debug print
            
            # Set the starting subtask for perform_research to the next phase after loaded_phase_name
            next_subtask = None
            found_loaded_phase = False
            for phase, subtasks in lab.phases:
                for subtask in subtasks:
                    if found_loaded_phase and next_subtask is None:
                        next_subtask = subtask.replace(" ", "_")
                        break
                    if subtask.replace(" ", "_") == loaded_phase_name:
                        found_loaded_phase = True
                if next_subtask:
                    break

            lab.start_subtask = next_subtask
            if lab.verbose: print(f"--- [INFO] Setting start_subtask for resuming to: {lab.start_subtask} (next phase after {loaded_phase_name})")


    else:
        # remove previous files only if not loading existing state
        remove_figures()
        remove_directory("research_dir")
        # make src and research directory
        if not os.path.exists("state_saves"):
            os.mkdir(os.path.join(".", "state_saves"))
        os.mkdir(os.path.join(".", "research_dir"))
        os.mkdir(os.path.join("./research_dir", "src"))
        os.mkdir(os.path.join("./research_dir", "tex"))

        lab = LaboratoryWorkflow(
            research_topic=research_topic,
            notes=task_notes_LLM,
            agent_model_backbone=agent_models,
            human_in_loop_flag=human_in_loop,
            openai_api_key=api_key,
            openai_base_url=openai_base_url,
            openai_model_name=openai_model_name,
            compile_pdf=compile_pdf,
            num_papers_lit_review=num_papers_lit_review,
            papersolver_max_steps=papersolver_max_steps,
            mlesolver_max_steps=mlesolver_max_steps,
            args=args,
            author_name=author_name, # Pass author_name
        )
    
    # Add debug print for initial phase status in perform_research
    if lab.verbose: print(f"--- [DEBUG] Initial phase status before perform_research: {lab.phase_status}") # Added debug print
    lab.perform_research()
