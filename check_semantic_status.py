#!/usr/bin/env python3
"""
检查Semantic Scholar API状态
"""

import os
import glob

def check_api_key():
    """检查API密钥配置"""
    api_key = os.getenv('SEMANTIC_SCHOLAR_API_KEY')
    if api_key:
        print(f"✅ API密钥已配置: {api_key[:10]}...")
        return True
    else:
        print("❌ 未找到API密钥")
        return False

def check_semantic_files():
    """检查是否有Semantic Scholar生成的文件"""
    semantic_files = glob.glob("research_dir/SEMANTIC_*.md")
    json_files = glob.glob("research_dir/semantic_scholar_*.json")
    
    if semantic_files or json_files:
        print(f"✅ 找到Semantic Scholar文件:")
        for f in semantic_files:
            print(f"  - {f}")
        for f in json_files:
            print(f"  - {f}")
        return True
    else:
        print("❌ 未找到Semantic Scholar生成的文件")
        return False

def check_references_section():
    """检查报告中是否有References章节"""
    try:
        with open("research_dir/research_paper.md", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "## References" in content or "## 9. References" in content:
            print("✅ 报告包含References章节")
            return True
        else:
            print("❌ 报告不包含References章节")
            return False
    except FileNotFoundError:
        print("❌ 未找到research_paper.md文件")
        return False

def check_semantic_integration():
    """检查Semantic Scholar集成模块"""
    try:
        from enhanced_semantic_search import SemanticScholarSearch
        print("✅ Semantic Scholar模块可用")
        
        # 尝试初始化
        search = SemanticScholarSearch()
        print("✅ 可以成功初始化SemanticScholarSearch")
        return True
    except ImportError as e:
        print(f"❌ Semantic Scholar模块不可用: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Semantic Scholar API 状态检查")
    print("=" * 50)
    
    checks = [
        ("API密钥配置", check_api_key),
        ("集成模块", check_semantic_integration),
        ("生成的文件", check_semantic_files),
        ("References章节", check_references_section),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n🔍 检查{name}...")
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 50)
    print("检查结果汇总")
    print("=" * 50)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 Semantic Scholar API 完全集成并可用！")
    else:
        print("\n⚠️  Semantic Scholar API 未完全配置或使用")
        print("\n建议:")
        print("1. 设置API密钥: export SEMANTIC_SCHOLAR_API_KEY='sk-xxxxxxxxx'")
        print("2. 重新运行程序并在Literature Review阶段使用SEMANTIC_SEARCH命令")
        print("3. 检查网络连接和API端点可访问性")
