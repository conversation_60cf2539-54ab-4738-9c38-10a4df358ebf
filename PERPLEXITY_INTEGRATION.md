# Perplexity AI - Agent Laboratory 的主要文献综述引擎

## 🚀 重大更新

**Perplexity AI 现已成为 Agent Laboratory 的主要文献综述引擎！**

Agent Laboratory 已经完全优化，将 Perplexity AI 设置为文献综述的首选工具，提供比传统 ArXiv 搜索更强大、更智能的研究能力。Perplexity AI 通过 AI 驱动的搜索和分析，访问包括 PubMed、ArXiv、Google Scholar 等在内的多个学术数据库，为研究人员提供专业级的文献综述体验。

## 🎯 核心优势

### 🥇 主要文献综述引擎
- **优先级**: Perplexity AI 现在是 Agent Laboratory 的首选文献综述工具
- **智能推荐**: 系统自动推荐使用 Perplexity 进行综合文献分析
- **无缝集成**: 与 PhD 学生代理完全集成，提供最佳用户体验

### 🔍 增强的文献搜索
- **多数据库覆盖**: 自动搜索 PubMed、ArXiv、Google Scholar、IEEE Xplore 等权威学术数据库
- **AI 驱动分析**: 使用先进的 AI 模型理解和分析学术内容
- **实时更新**: 获取最新的研究进展和发现
- **智能过滤**: 根据研究领域自动选择最相关的数据库

### 📚 专业级文献综述生成
- **综合分析**: 多维度文献分析，包括方法论、最新发展、研究空白
- **自动结构化**: 生成包含背景、现状、方法、趋势、挑战的完整综述
- **引用管理**: 自动生成和管理引用文献，符合学术标准
- **多语言支持**: 支持中文等多种语言的文献综述
- **成本优化**: 智能预算管理和成本控制

### ❓ 智能研究问题解答
- **专业回答**: 针对具体研究问题提供详细、有据可查的答案
- **上下文理解**: 结合研究背景提供更准确的回答
- **趋势分析**: 识别研究趋势和未来方向

## 安装和配置

### 1. 获取 Perplexity API Key

访问 [Perplexity AI](https://www.perplexity.ai/) 官网注册账户并获取 API Key。

### 2. 设置环境变量

```bash
export PERPLEXITY_API_KEY="your_perplexity_api_key_here"
```

### 3. 安装依赖

项目已自动包含所需依赖，运行以下命令确保安装：

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
python ai_lab_repo.py \
  --research-topic "你的研究主题" \
  --api-key "你的Google API Key" \
  --perplexity-api-key "你的Perplexity API Key" \
  --literature-engine "both" \
  --language "中文"
```

### 命令行参数

- `--perplexity-api-key`: Perplexity API 密钥
- `--literature-engine`: 文献搜索引擎选择
  - `arxiv`: 仅使用 ArXiv 搜索
  - `perplexity`: 仅使用 Perplexity 搜索
  - `both`: 同时使用两种引擎（推荐）
- `--use-perplexity`: 启用/禁用 Perplexity 功能 (True/False)

### 文献综述阶段的新命令

在文献综述阶段，PhD 学生代理现在可以使用以下命令：

1. **传统 ArXiv 搜索**:
   ```
   ```SUMMARY
   搜索查询
   ```
   ```

2. **Perplexity 增强文献综述**:
   ```
   ```PERPLEXITY_REVIEW
   研究主题
   ```
   ```

3. **获取完整论文文本**:
   ```
   ```FULL_TEXT
   arXiv论文ID
   ```
   ```

4. **添加论文到综述**:
   ```
   ```ADD_PAPER
   arXiv论文ID
   论文摘要
   ```
   ```

## 测试集成

运行测试脚本验证 Perplexity 集成是否正常工作：

```bash
python test_perplexity.py
```

测试脚本将验证：
- Perplexity API 连接
- 文献搜索功能
- 文献综述生成
- 研究问题解答
- 与推理模块的集成

## 成本估算

Perplexity API 的定价（大致）：

| 模型 | 输入成本 | 输出成本 |
|------|----------|----------|
| Sonar Pro | $1.00/1M tokens | $1.00/1M tokens |
| Sonar Small | $0.20/1M tokens | $0.20/1M tokens |
| Sonar Huge | $5.00/1M tokens | $5.00/1M tokens |

Agent Laboratory 会自动跟踪和显示 API 使用成本。

## 最佳实践

### 1. 选择合适的搜索策略
- 对于快速概览，使用 ArXiv 搜索
- 对于深入分析，使用 Perplexity 综述生成
- 对于全面覆盖，选择 "both" 模式

### 2. 优化搜索查询
- 使用具体、明确的研究术语
- 避免过于宽泛的查询
- 结合领域特定的关键词

### 3. 管理 API 成本
- 合理设置文献数量限制
- 优先使用较小的模型进行初步搜索
- 监控 API 使用情况

## 故障排除

### 常见问题

1. **API Key 错误**
   - 确认 API Key 正确设置
   - 检查环境变量是否正确配置

2. **网络连接问题**
   - 确认网络连接正常
   - 检查防火墙设置

3. **搜索结果为空**
   - 尝试调整搜索查询
   - 检查研究领域是否在数据库覆盖范围内

### 调试模式

启用详细日志输出：

```bash
python ai_lab_repo.py --verbose [其他参数...]
```

## 贡献和反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 检查现有的 Issues
2. 创建新的 Issue 描述问题
3. 提供详细的错误信息和复现步骤

## 更新日志

### v1.0.0 (2025-01-14)
- 初始集成 Perplexity AI
- 添加多引擎文献搜索支持
- 实现智能文献综述生成
- 集成成本跟踪功能
- 添加中文支持

---

**注意**: 这是一个实验性功能，我们会根据用户反馈持续改进和优化。
