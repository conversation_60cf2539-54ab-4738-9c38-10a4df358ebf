#!/usr/bin/env python3
"""
测试图片插入修复的脚本
"""

import os
import glob
from papersolver import PaperSolver

def test_image_processing():
    """测试图片处理功能"""
    
    # 获取图片文件列表
    image_folder = "silicon_analysis_plots_20250628_034822"
    if os.path.exists(image_folder):
        image_files = glob.glob(f"{image_folder}/*.png")
        print(f"找到 {len(image_files)} 个图片文件:")
        for img in image_files[:5]:  # 只显示前5个
            print(f"  - {img}")
    else:
        print(f"图片文件夹 {image_folder} 不存在")
        return
    
    # 创建PaperSolver实例
    solver = PaperSolver(
        llm_str="test",
        generated_images=image_files
    )
    
    # 测试相对路径转换
    print("\n测试相对路径转换:")
    test_path = "silicon_analysis_plots_20250628_034822/target_distribution.png"
    relative_path = solver.get_relative_image_path(test_path)
    print(f"原始路径: {test_path}")
    print(f"相对路径: {relative_path}")
    
    # 测试alt文本生成
    print("\n测试alt文本生成:")
    test_images = [
        "silicon_analysis_plots_20250628_034822/target_distribution.png",
        "silicon_analysis_plots_20250628_034822/stacking_level2_meta_learner_shap_bar.png",
        "silicon_analysis_plots_20250628_034822/model_comparison_results.png"
    ]
    
    for img in test_images:
        alt_text = solver.generate_alt_text(img)
        print(f"图片: {os.path.basename(img)}")
        print(f"Alt文本: {alt_text}")
        print()
    
    # 测试不同section的图片获取
    print("测试不同section的图片获取:")
    sections = ["methods", "results", "discussion"]
    
    for section in sections:
        available_images = solver.get_available_images_for_section(section)
        print(f"\n{section} section 可用图片: {len(available_images)}")
        for i, img_info in enumerate(available_images[:3]):  # 只显示前3个
            print(f"  {i+1}. {img_info['alt_text']}")
            print(f"     路径: {img_info['path']}")
    
    # 测试图片使用跟踪
    print("\n测试图片使用跟踪:")
    test_response = """
    这是一个测试响应，包含图片引用：
    ![目标变量分布图](../silicon_analysis_plots_20250628_034822/target_distribution.png)
    ![模型性能对比](../silicon_analysis_plots_20250628_034822/model_comparison_results.png)
    """
    
    print("处理前已使用图片数量:", len(solver.used_images))
    solver.process_image_usage_in_response(test_response)
    print("处理后已使用图片数量:", len(solver.used_images))
    print("已使用的图片:", list(solver.used_images))

if __name__ == "__main__":
    test_image_processing()
