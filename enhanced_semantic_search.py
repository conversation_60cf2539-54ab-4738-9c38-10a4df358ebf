#!/usr/bin/env python3
"""
Enhanced Semantic Scholar Search for Literature Review
整合到Agent Laboratory的增强语义搜索模块
"""

import requests
import json
import os
import datetime
import re
import time
from typing import List, Dict, Optional, Tuple
from utils import save_text_to_markdown


class SemanticScholarSearch:
    """
    Enhanced Semantic Scholar API wrapper for academic literature search
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize Semantic Scholar search
        @param api_key: Optional API key for higher rate limits
        """
        self.base_url = 'https://api.semanticscholar.org/graph/v1'

        # Try to get API key from parameter, environment variable, or use None
        self.api_key = api_key or os.getenv('SEMANTIC_SCHOLAR_API_KEY')

        self.headers = {}
        if self.api_key:
            self.headers['x-api-key'] = self.api_key
            print("✅ Semantic Scholar API key configured")
        else:
            print("⚠️  No Semantic Scholar API key found. Using public API with rate limits.")
        
        # Reference storage for citation management
        self.collected_references = []
        self.reference_counter = 1
    
    def search_papers(self, query: str, limit: int = 20, fields: List[str] = None) -> Dict:
        """
        Search for papers using Semantic Scholar API
        @param query: Search query string
        @param limit: Maximum number of results
        @param fields: List of fields to retrieve
        @return: Search results dictionary
        """
        if fields is None:
            fields = ['title', 'authors', 'year', 'abstract', 'url', 'citationCount', 'venue', 'paperId']
        
        url = f'{self.base_url}/paper/search'
        params = {
            'query': query,
            'fields': ','.join(fields),
            'limit': limit
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(url, params=params, headers=self.headers)

                if response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt  # Exponential backoff
                    print(f"Rate limit hit, waiting {wait_time} seconds... (attempt {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue

                response.raise_for_status()
                return response.json()

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:  # Last attempt
                    print(f"Error searching papers after {max_retries} attempts: {e}")
                    return {'data': []}
                else:
                    print(f"Attempt {attempt + 1} failed, retrying...")
                    time.sleep(1)

        return {'data': []}
    
    def get_paper_details(self, paper_id: str, fields: List[str] = None) -> Dict:
        """
        Get detailed information about a specific paper
        @param paper_id: Semantic Scholar paper ID
        @param fields: List of fields to retrieve
        @return: Paper details dictionary
        """
        if fields is None:
            fields = ['title', 'authors', 'year', 'abstract', 'url', 'citationCount', 'venue', 'references', 'citations']
        
        url = f'{self.base_url}/paper/{paper_id}'
        params = {'fields': ','.join(fields)}
        
        try:
            response = requests.get(url, params=params, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error getting paper details: {e}")
            return {}
    
    def format_paper_for_review(self, paper: Dict) -> str:
        """
        Format a paper for literature review
        @param paper: Paper dictionary from API
        @return: Formatted string for literature review
        """
        title = paper.get('title', 'Unknown Title')
        authors = paper.get('authors', [])
        year = paper.get('year', 'Unknown Year')
        abstract = paper.get('abstract', 'No abstract available')
        venue = paper.get('venue', 'Unknown Venue')
        citation_count = paper.get('citationCount', 0)
        url = paper.get('url', '')
        
        # Format authors
        author_names = []
        for author in authors[:3]:  # Limit to first 3 authors
            if isinstance(author, dict) and 'name' in author:
                author_names.append(author['name'])
            elif isinstance(author, str):
                author_names.append(author)
        
        if len(authors) > 3:
            author_str = ', '.join(author_names) + ' et al.'
        else:
            author_str = ', '.join(author_names)
        
        # Create citation key for reference
        first_author_last = author_names[0].split()[-1] if author_names else 'Unknown'
        citation_key = f"{first_author_last}{year}"
        
        # Store reference for later citation
        reference_entry = {
            'key': citation_key,
            'title': title,
            'authors': author_str,
            'year': year,
            'venue': venue,
            'url': url,
            'citation_count': citation_count
        }
        self.collected_references.append(reference_entry)
        
        formatted_text = f"""
**{title}** ({citation_key})
Authors: <AUTHORS>
Year: {year}
Venue: {venue}
Citations: {citation_count}

Abstract: {abstract}

URL: {url}
---
"""
        return formatted_text
    
    def generate_literature_review(self, query: str, num_papers: int = 10) -> Tuple[str, List[Dict]]:
        """
        Generate a comprehensive literature review for a given query
        @param query: Research query
        @param num_papers: Number of papers to include
        @return: Tuple of (formatted review text, list of references)
        """
        print(f"🔍 Searching Semantic Scholar for: {query}")
        
        # Search for papers
        results = self.search_papers(query, limit=num_papers)
        
        if not results.get('data'):
            return "No papers found for the given query.", []
        
        papers = results['data']
        print(f"📚 Found {len(papers)} papers")
        
        # Generate review text
        review_text = f"# Literature Review: {query}\n\n"
        review_text += f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # Sort papers by citation count (most cited first)
        papers_sorted = sorted(papers, key=lambda x: x.get('citationCount', 0), reverse=True)
        
        review_text += "## Overview\n\n"
        review_text += f"This literature review covers {len(papers)} relevant papers from Semantic Scholar database. "
        review_text += "Papers are organized by relevance and citation impact.\n\n"
        
        review_text += "## Key Papers\n\n"
        
        for i, paper in enumerate(papers_sorted, 1):
            review_text += f"### {i}. {self.format_paper_for_review(paper)}\n"
        
        return review_text, self.collected_references
    
    def save_search_results(self, query: str, results: Dict, output_dir: str = "./research_dir") -> str:
        """
        Save search results to a JSON file
        @param query: Search query
        @param results: Search results
        @param output_dir: Output directory
        @return: Saved file path
        """
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_query = re.sub(r'[^\w\s\-\.\u4e00-\u9fa5]', '', query.strip()).replace(' ', '_')
        if not filename_query:
            filename_query = "semantic_search"
        
        filename = f"semantic_scholar_{filename_query}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        # Save results
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=4)
            print(f"✅ Search results saved to: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ Error saving search results: {e}")
            return ""
    
    def generate_references_section(self) -> str:
        """
        Generate a properly formatted References section
        @return: Formatted references string
        """
        if not self.collected_references:
            return ""
        
        references_text = "## References\n\n"
        
        # Sort references alphabetically by first author's last name
        sorted_refs = sorted(self.collected_references, key=lambda x: x['key'])
        
        for i, ref in enumerate(sorted_refs, 1):
            # Format in academic style
            ref_text = f"{i}. {ref['authors']} ({ref['year']}). "
            ref_text += f"{ref['title']}. "
            if ref['venue'] and ref['venue'] != 'Unknown Venue':
                ref_text += f"*{ref['venue']}*. "
            if ref['url']:
                ref_text += f"Available at: {ref['url']}"
            if ref['citation_count'] > 0:
                ref_text += f" (Cited by {ref['citation_count']})"
            
            references_text += ref_text + "\n\n"
        
        return references_text
    
    def clear_references(self):
        """Clear collected references"""
        self.collected_references = []
        self.reference_counter = 1


def test_semantic_search():
    """Test function for semantic search"""
    search = SemanticScholarSearch()
    
    # Test search
    query = "machine learning SHAP explainable AI"
    review_text, references = search.generate_literature_review(query, num_papers=5)
    
    print("Generated Review:")
    print(review_text[:500] + "...")
    print(f"\nCollected {len(references)} references")
    
    # Test references section
    refs_section = search.generate_references_section()
    print("\nReferences Section:")
    print(refs_section[:300] + "...")


if __name__ == "__main__":
    test_semantic_search()
