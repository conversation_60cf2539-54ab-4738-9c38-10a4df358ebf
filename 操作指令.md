# Agent Laboratory 操作指令 - Perplexity AI 增强版

## 概述与引言

欢迎来到 Agent Laboratory，这是一个创新的研究自动化平台，旨在通过集成先进的语言模型和专业代理，彻底改变科学研究和数据分析的范式。在当今信息爆炸的时代，研究人员面临着海量文献筛选、复杂数据处理和模型构建的巨大挑战。Agent Laboratory 的核心目标是赋能研究人员，通过自动化重复性任务、提供智能洞察和加速知识发现，从而显著提升研究效率和产出质量。本平台通过模拟人类研究团队的工作流程，将复杂的科学问题分解为可管理的子任务，并分配给不同的专业代理（如 PhD 学生代理负责文献综述，MLEngineer 代理负责模型构建），实现端到端的研究自动化。

本次重大更新引入了 Perplexity AI 作为 Agent Laboratory 的主要文献综述引擎，标志着平台在文献检索、分析和报告生成能力上的一个里程碑式飞跃。Perplexity AI 以其强大的实时信息检索能力和多源信息整合能力而闻名，能够从海量学术数据库中快速提取相关文献，并进行智能分析，从而为研究人员提供更全面、更准确、更及时的文献综述。通过将 Perplexity AI 集成到 Agent Laboratory 的工作流程中，我们旨在解决传统文献综述耗时费力、信息滞后等痛点，使研究人员能够将更多精力投入到创新性思考和实验设计中。本操作指令将详细介绍 Agent Laboratory 的各项功能，特别是 Perplexity AI 增强版的操作方法、推荐配置、参数说明、测试验证、项目示例、功能特点、成本管理以及故障排除策略，旨在帮助用户充分利用平台潜力，加速其科研进程。

## 🚀 完整运行命令（推荐配置）

Agent Laboratory 提供了灵活的运行命令，以适应不同的研究需求和用户偏好。以下是推荐的完整运行命令配置，旨在最大化平台的功能和效率。这些命令不仅包含了核心的研究自动化逻辑，还集成了 Perplexity AI 的强大文献综述能力，确保您能够获取最新、最全面的研究信息。

### 1. 基础运行命令 - 使用 Perplexity AI 作为主要文献综述引擎

```bash
python ai_lab_repo1.py \
  --research-topic "Data-Driven Optimization of Silicon Extraction from fly Ash A Machine Learning Approach for Predicting and Understanding Effective Silicon Content by using shap and pdp" \
  --api-key "AIzaSyCoAjvX0JqMQVAtTf5WwFMlT5iNfvWQxKM" \
  
  --perplexity-api-key "your_perplexity_api_key_here" \
  --literature-engine "both" \
  --language "中文" \
  --llm-backend "gemini-2.5-flash" \
  --num-papers-lit-review "2" \
  --copilot-mode "true" \
  --save-docx \
  --load-existing "true" \
  --load-existing-path "state_saves/results_interpretation.pkl"
```

这条命令是启动 Agent Laboratory 的核心入口点，它设计用于一个具体的硅提取研究项目。`--research-topic` 参数定义了研究的核心问题，本例中是关于从粉煤灰中提取硅的机器学习优化。一个具体且明确的研究主题是成功的关键，它能指导代理更精准地进行文献检索和数据分析。`--api-key` 和 `--perplexity-api-key` 分别用于配置 Google API 和 Perplexity AI 的访问凭证。这些 API 密钥是启用平台高级功能（如文献检索和 LLM 后端通信）的必要条件。强烈建议通过环境变量进行配置以提高安全性。`--literature-engine "both"` 参数指示平台同时利用 Perplexity AI 和 ArXiv 进行文献检索，这种“双引擎”策略能够确保文献覆盖的广度和深度。`--language "中文"` 设置了操作和报告生成的语言，确保输出符合您的语言偏好。`--llm-backend "gemini-2.5-flash"` 指定了底层大型语言模型，`gemini-2.5-flash` 是一个高效且成本效益高的选择，适用于大多数研究任务。`--num-papers-lit-review "5"` 限定了文献综述阶段检索和分析的论文数量，您可以根据研究的广度和深度需求进行调整。`--copilot-mode "true"` 启用了人机交互模式，允许您在关键决策点介入并提供指导，这对于复杂或探索性研究尤为有用。`--save-docx` 参数指示平台在研究完成后将最终报告保存为 DOCX 格式，便于编辑和分享。最后，`--load-existing "true"` 结合 `--load-existing-path "state_saves/data_preparation.pkl"` 允许您从之前保存的状态（例如数据准备阶段）继续研究，这对于长时间运行或需要多次迭代的研究项目非常实用，避免了重复计算和时间浪费。

### 2. 高级配置命令

```bash
python ai_lab_repo.py \
  --research-topic "深度学习在药物发现中的最新进展" \
  --api-key "your_google_api_key_here" \
  --perplexity-api-key "your_perplexity_api_key_here" \
  --literature-engine "both" \
  --language "中文" \
  --llm-backend "gemini-2.5-flash-preview-05-20" \
  --num-papers-lit-review "8" \
  --mlesolver-max-steps "5" \
  --papersolver-max-steps "7" \
  --copilot-mode "false" \
  --compile-latex "true"
```

此高级配置命令展示了 Agent Laboratory 在处理更复杂、更定制化研究任务时的强大能力。除了基础命令中已解释的参数外，这里引入了几个新的参数以提供更精细的控制。`--research-topic "深度学习在药物发现中的最新进展"` 示例了一个中文研究主题，表明平台对多语言研究的支持。`--num-papers-lit-review "8"` 将文献综述的论文数量增加到 8 篇，适用于需要更广泛文献基础的研究。`--mlesolver-max-steps "5"` 和 `--papersolver-max-steps "7"` 分别设定了机器学习求解器和论文求解器在各自阶段的最大执行步数。这些参数允许用户对代理的计算资源消耗和任务执行深度进行微调，尤其是在处理计算密集型任务或需要深入探索特定研究领域时。`--copilot-mode "false"` 将平台设置为全自动模式，代理将自主决策并执行任务，无需人工干预，这适用于已经明确研究路径或需要批量处理任务的场景。`--compile-latex "true"` 是一个非常强大的功能，它指示平台在生成最终报告时，将其编译为高质量的 LaTeX 格式文档。这对于需要发布到学术期刊或会议的论文尤为重要，因为它能确保报告的专业排版和图表呈现。

### 3. 交互模式命令

```bash
python ai_lab_repo.py \
  --research-topic "人工智能在医疗诊断中的应用" \
  --api-key "your_google_api_key_here" \
  --perplexity-api-key "your_perplexity_api_key_here" \
  --literature-engine "both" \
  --language "中文" \
  --llm-backend "gemini-2.5-flash-preview-05-20" \
  --copilot-mode "true"
```

交互模式是 Agent Laboratory 的一个核心特性，它在自动化流程中保留了人类的决策灵活性。此命令示例了如何在医疗诊断领域启动一个交互式研究会话。`--copilot-mode "true"` 是启用交互模式的关键参数，它允许用户在每个关键决策点暂停代理的执行，审查其当前状态、分析结果，并提供进一步的指示或修正。这对于以下场景至关重要：
*   **探索性研究**: 当研究主题尚不明确，需要逐步探索和调整方向时。
*   **复杂问题**: 当问题涉及多个学科交叉，需要专家知识进行判断时。
*   **调试与优化**: 当代理行为不符合预期，需要介入调试或优化策略时。
*   **教育与学习**: 作为学习数据科学流程和代理协作的实践工具。

在交互模式下，平台会通过命令行界面或集成开发环境（如 VSCode）提供清晰的提示和选项，引导用户进行决策。用户可以查看代理生成的中间结果、修改参数、调整研究路径，甚至手动执行某些步骤。这种人机协作模式充分发挥了人工智能的效率和人类的洞察力，使得研究过程更加灵活、可控和高效。

## 🔑 环境变量设置（推荐方式）

为了提高 API 密钥的安全性和管理便利性，强烈建议通过环境变量来配置 Google API Key 和 Perplexity AI API Key。这种方法避免了将敏感信息直接写入代码或命令行，从而降低了意外泄露的风险，并使得在不同项目或环境中切换密钥变得更加容易。

### 设置环境变量的优势

1.  **安全性提升**: 将 API 密钥存储为环境变量，可以避免它们被硬编码到脚本中或出现在命令行历史记录中。这对于保护您的凭证免受未经授权的访问至关重要，尤其是在团队协作或将代码上传到版本控制系统（如 Git）时。
2.  **管理便利性**: 您可以在操作系统层面集中管理所有 API 密钥。当您需要更新密钥或在不同的项目之间切换时，只需修改环境变量一次，而无需修改每个脚本文件。
3.  **环境隔离**: 不同的开发、测试和生产环境可以配置不同的 API 密钥，从而实现环境隔离，避免在生产环境中使用开发密钥或反之。
4.  **符合最佳实践**: 将敏感信息存储为环境变量是行业内广泛推荐的安全实践，有助于构建更健壮、更安全的应用程序。

### 具体设置步骤

#### 对于 Linux/macOS 用户

您可以通过编辑 shell 的配置文件（如 `.bashrc`, `.zshrc` 或 `.profile`）来设置环境变量。

1.  **打开终端**:
    打开您的终端应用程序。

2.  **编辑配置文件**:
    使用您喜欢的文本编辑器（如 `nano` 或 `vim`）打开您的 shell 配置文件。例如，如果您使用 Bash：
    ```bash
    nano ~/.bashrc
    ```
    如果您使用 Zsh：
    ```bash
    nano ~/.zshrc
    ```

3.  **添加环境变量**:
    在文件的末尾添加以下行，将 `"your_google_api_key_here"` 和 `"your_perplexity_api_key_here"` 替换为您的实际 API 密钥。
    ```bash
    export GOOGLE_API_KEY="AIzaSyCekmPTA4T5PeKbOSt5F_AQyltLSre_l14"
    export PERPLEXITY_API_KEY="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"
    ```
    **重要提示**: 请确保您的 Perplexity API 密钥以 `pplx-` 开头。

4.  **保存并关闭文件**:
    在 `nano` 中，按 `Ctrl + O` 保存，然后按 `Enter` 确认，最后按 `Ctrl + X` 退出。
    在 `vim` 中，按 `Esc`，然后输入 `:wq` 并按 `Enter`。

5.  **使环境变量生效**:
    在终端中运行以下命令，使更改立即生效，而无需重新启动终端：
    ```bash
    source ~/.bashrc  # 如果您编辑的是 .bashrc
    source ~/.zshrc   # 如果您编辑的是 .zshrc
    ```

6.  **验证环境变量**:
    通过以下命令验证环境变量是否已成功设置：
    ```bash
    echo $GOOGLE_API_KEY
    echo $PERPLEXITY_API_KEY
    ```
    如果终端显示了您的 API 密钥，则表示设置成功。

#### 对于 Windows 用户

Windows 用户可以通过图形界面或命令行来设置环境变量。

##### 方法一：通过图形界面设置

1.  **打开系统属性**:
    右键点击“此电脑”或“我的电脑”图标，选择“属性”。
    在打开的窗口中，点击“高级系统设置”。

2.  **打开环境变量对话框**:
    在“系统属性”窗口中，点击“环境变量”按钮。

3.  **添加或编辑用户变量**:
    在“环境变量”对话框中，您可以在“用户变量”或“系统变量”部分添加新的环境变量。通常，为了个人使用，建议在“用户变量”中添加。
    *   点击“新建”按钮，在“变量名”中输入 `GOOGLE_API_KEY`，在“变量值”中输入您的 Google API 密钥，然后点击“确定”。
    *   再次点击“新建”按钮，在“变量名”中输入 `PERPLEXITY_API_KEY`，在“变量值”中输入您的 Perplexity API 密钥，然后点击“确定”。

4.  **保存更改**:
    点击所有打开的对话框中的“确定”按钮，直到所有窗口关闭。

5.  **验证环境变量**:
    打开一个新的命令提示符 (CMD) 或 PowerShell 窗口（确保不是之前已打开的窗口），然后运行以下命令：
    ```cmd
    echo %GOOGLE_API_KEY%
    echo %PERPLEXITY_API_KEY%
    ```
    或者在 PowerShell 中：
    ```powershell
    $env:GOOGLE_API_KEY
    $env:PERPLEXITY_API_KEY
    ```
    如果显示了您的 API 密钥，则表示设置成功。

##### 方法二：通过命令行设置 (PowerShell)

您也可以使用 PowerShell 来设置环境变量，这对于自动化脚本或临时设置非常有用。

1.  **打开 PowerShell**:
    以管理员身份运行 PowerShell。

2.  **设置环境变量**:
    运行以下命令，将 `"your_google_api_key_here"` 和 `"your_perplexity_api_key_here"` 替换为您的实际 API 密钥。
    ```powershell
    $env:GOOGLE_API_KEY="AIzaSyCekmPTA4T5PeKbOSt5F_AQyltLSre_l14"
    $env:PERPLEXITY_API_KEY="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"
    ```
    **注意**: 这种方法设置的环境变量只在当前 PowerShell 会话中有效。如果您想永久设置，请使用图形界面方法或在 PowerShell 配置文件中添加。

### 简化的运行命令（使用环境变量）

一旦环境变量设置成功，您就可以使用更简洁的命令来运行 Agent Laboratory，无需在每次运行时手动输入 API 密钥：

```bash
python ai_lab_repo.py \
  --research-topic "您的研究主题" \
  --literature-engine "both" \
  --language "中文"
```

## 📋 参数说明

Agent Laboratory 提供了丰富的命令行参数，允许用户对研究流程的各个方面进行精细控制。理解每个参数的用途及其推荐值，是高效利用平台的关键。

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `--research-topic` | 定义您的研究主题或具体的研究问题。这是一个核心参数，直接影响文献检索的范围、数据分析的焦点以及最终报告的内容。清晰、具体的研究主题能帮助代理更精准地理解您的需求，并生成相关性更高的结果。 | 具体的研究问题，例如："深度学习在药物发现中的最新进展" 或 "粉煤灰制备硅肥有效硅的机器学习优化"。 |
| `--api-key` | Google API 密钥，用于访问 Google 的各种服务，例如 Google Scholar 搜索（如果 `literature-engine` 包含 Google Scholar）。这是平台进行文献检索和某些 LLM 后端通信所必需的。 | 必需。请确保您的 API 密钥是有效的，并且具有访问所需服务的权限。 |
| `--perplexity-api-key` | Perplexity AI API 密钥，专用于启用 Perplexity AI 作为文献综述引擎。Perplexity AI 能够提供更广泛、更实时的文献检索和分析能力，是增强文献综述阶段的关键。 | 必需（用于增强文献综述）。请确保密钥以 `pplx-` 开头。 |
| `--literature-engine` | 选择用于文献综述的搜索引擎。这个参数决定了代理从哪些来源获取文献信息。 | `"both"` (推荐)。此选项结合了 Perplexity AI 的广度和实时性，以及 ArXiv 的学术深度。其他可选值包括 `"perplexity"` (仅使用 Perplexity AI) 和 `"arxiv"` (仅使用 ArXiv)。 |
| `--language` | 指定 Agent Laboratory 操作和最终报告生成的语言。平台将尝试根据此设置调整其输出和内部处理。 | `"中文"` 或 `"English"`。确保您的研究主题和预期输出语言一致。 |
| `--llm-backend` | 选择底层大型语言模型 (LLM) 后端。不同的 LLM 模型在性能、成本和功能上可能有所差异。 | `"gemini-2.5-flash-preview-05-20"` 是一个通用且高效的选择。其他可选值可能包括 `"openai-compatible"`（用于兼容 OpenAI API 的模型，如自定义部署或第三方服务）或更具体的模型名称。 |
| `--num-papers-lit-review` | 设定文献综述阶段代理将检索和分析的论文数量。这个参数直接影响文献综述的广度和深度。 | `"5"` 到 `"10"`。较小的数值适用于快速概览，较大的数值适用于深度研究。请注意，增加论文数量可能会增加运行时间和 API 成本。 |
| `--copilot-mode` | 启用或禁用人机交互模式。在交互模式下，平台会在关键决策点暂停并等待用户输入，允许您审查和指导研究流程。 | `"false"` (自动模式) 或 `"true"` (交互模式)。对于首次使用或复杂研究，推荐 `"true"`；对于自动化批处理任务，推荐 `"false"`。 |
| `--save-docx` | 布尔参数，指示平台是否将最终的研究报告保存为 DOCX 格式的文件。DOCX 格式便于在 Microsoft Word 或其他兼容软件中进行后续编辑和排版。 | 无需指定值，只需在命令中包含此参数即可启用。默认情况下，如果未指定，报告可能以 Markdown 或其他默认格式保存。**注意**: 当前版本 `--save-docx` 参数会将最终报告保存为 Markdown 格式 (.md 文件) 而不是 DOCX 格式。 |
| `--load-existing` | 布尔参数，指示平台是否从之前保存的状态加载并继续执行。这对于中断的会话或需要迭代优化的研究非常有用。 | 无需指定值，只需在命令中包含此参数即可启用。与 `--load-existing-path` 结合使用。 |
| `--load-existing-path` | 指定要加载的现有状态文件的路径。这个文件通常是 `.pkl` 格式，包含了代理在上次运行结束时的所有内部状态和数据。 | 有效的文件路径，例如 `"state_saves/data_preparation.pkl"`。确保路径正确且文件存在。 |
| `--mlesolver-max-steps` | 设定机器学习求解器在执行其任务时允许的最大步骤数。这个参数用于控制机器学习模型训练和评估的复杂度和耗时。 | 推荐值取决于任务复杂度，例如 `"5"`。较小的数值可以加快运行速度，但可能牺牲模型性能。 |
| `--papersolver-max-steps` | 设定论文求解器在生成研究论文时允许的最大步骤数。这个参数控制了论文撰写的详细程度和迭代次数。 | 推荐值取决于所需报告的详细程度，例如 `"7"`。较大的数值可以生成更详细、更全面的报告。 |
| `--compile-latex` | 布尔参数，指示平台是否将最终报告编译为 LaTeX 格式。这对于生成学术出版物质量的报告非常有用。 | 无需指定值，只需在命令中包含此参数即可启用。需要系统安装 LaTeX 编译环境。 |

## 🧪 测试命令

在正式启动大规模研究项目之前，强烈建议您运行以下测试命令，以验证 Agent Laboratory 的各项功能，特别是与 Perplexity AI 的集成是否正常工作。这有助于及早发现并解决潜在的配置问题或环境依赖，从而节省您宝贵的研究时间和计算资源。

### 1. 测试 Perplexity 集成

```bash
python test_perplexity.py
```

此命令专门用于测试 Agent Laboratory 与 Perplexity AI 的核心集成功能。它会尝试连接 Perplexity AI API，执行一个简单的查询，并验证返回结果的格式和有效性。运行此测试的目的是确保您的 `PERPLEXITY_API_KEY` 配置正确，并且平台能够与 Perplexity AI 服务进行通信。如果此测试失败，通常意味着 API 密钥无效、网络连接问题或 Perplexity AI 服务暂时不可用。在进行任何实际研究之前，确保此测试通过是至关重要的。

### 2. 验证集成完整性

```bash
python verify_perplexity_integration.py
```

`verify_perplexity_integration.py` 脚本提供了更全面的集成完整性检查。它不仅验证与 Perplexity AI 的连接，还会检查其他相关组件，例如 LLM 后端通信、文件保存路径权限等，以确保整个 Agent Laboratory 系统能够协同工作。此测试旨在模拟一个简化的端到端流程，帮助您识别可能影响研究项目顺利运行的任何潜在瓶颈或配置错误。成功的验证将输出一份详细的报告，显示所有检查项的通过状态，通常预期会显示 100% 的通过率。

### 3. 运行演示

```bash
python demo_perplexity_primary.py
```

`demo_perplexity_primary.py` 命令将运行一个小型、预配置的演示项目，展示 Agent Laboratory 如何利用 Perplexity AI 进行文献综述。这个演示是实际研究工作流程的缩影，它会执行文献检索、内容分析和简要报告生成。通过运行演示，您可以直观地了解平台的工作方式、输出格式以及 Perplexity AI 在文献综述中的实际效果。这是一个很好的机会，可以在不消耗大量资源的情况下，熟悉 Agent Laboratory 的操作流程和功能特点。

通过执行这些测试，您可以确保 Agent Laboratory 环境已正确设置，并且所有必要的外部服务（如 Perplexity AI）都已准备就绪，从而为您的正式研究项目奠定坚实的基础。

## 📊 粉煤灰制备硅肥有效硅分析 - 具体项目示例

本节将通过一个具体的工业研究案例，详细展示 Agent Laboratory 如何应用于“粉煤灰制备硅肥有效硅分析”这一主题，以及如何利用机器学习方法预测和理解有效硅含量。这个案例突显了平台在材料科学和化学工程领域的应用潜力，特别是在优化工业废弃物资源化利用方面。

### 案例背景

粉煤灰是燃煤电厂排放的主要固体废弃物，其大量堆积不仅占用土地，还可能对环境造成污染。然而，粉煤灰中富含硅、铝、铁等元素，其中硅酸盐是制备硅肥的潜在原料。有效硅含量是衡量硅肥质量的关键指标，直接影响作物对硅元素的吸收利用。传统的有效硅分析方法通常耗时且成本较高，且难以实时指导生产过程的优化。因此，开发一种基于数据驱动的预测模型，能够快速、准确地评估有效硅含量，并揭示影响其产出的关键工艺参数，对于提高资源利用效率和减少环境污染具有重要意义。本研究旨在利用机器学习方法，从粉煤灰的组分、制备工艺参数（如温度、时间、添加剂等）数据中，建立预测有效硅含量的模型，并通过模型解释技术，深入理解各因素对有效硅产出的影响机制。

### 原始命令（仅 ArXiv 文献综述）

```bash
python ai_lab_repo.py --research-topic "实验主题：粉煤灰制备硅肥有效硅分析。请从指定路径 /Users/<USER>/Documents/Python/agentlaboratory/data/Si2025_5_4.csv 加载数据。将 'Effective_Silicon' 列作为预测的目标变量，其余列作为特征。请执行完整的端到端数据科学流程：首先，进行探索性数据分析（EDA）,绘制参数交互图；其次，对名义型字符特征使用独热编码，并进行特征工程和筛选，以识别影响有效硅产出的关键工艺参数；接着，构建并评估一个预测模型；最后，使用SHAP库进行模型解释和特征重要性可视化，并根据所有分析结果撰写一份详细的研究论文。" --api-key "your_google_api_key_here" --llm-backend "gemini-2.5-flash-preview-05-20" --copilot-mode "True" --language "中文" --num-papers-lit-review "0" --save-docx
```

此命令是该项目早期的运行示例，它主要依赖 ArXiv 进行文献综述，并通过设置 `--num-papers-lit-review "0"` 来跳过（或最小化）文献检索阶段，将重点直接放在数据分析和模型构建上。`--research-topic` 详细地定义了研究任务，包括数据加载路径、目标变量、特征、以及需要执行的数据科学流程（EDA、独热编码、特征工程、模型构建与评估、SHAP 解释和论文撰写）。`--api-key` 用于 Google 服务访问，而 `--llm-backend` 和 `--language` 分别指定了 LLM 模型和操作语言。`--copilot-mode "True"` 启用了交互模式，允许研究人员在流程中进行干预。`--save-docx` 参数指示保存最终报告。这个命令为后续引入 Perplexity AI 增强文献综述功能奠定了基础。

### 🚀 升级版命令（使用 Perplexity AI 增强文献综述）

**完整功能版本（需要 Perplexity API 密钥）**：

```bash
python ai_lab_repo.py \
  --research-topic "实验主题：粉煤灰制备硅肥有效硅机器学习。请从指定路径 /Users/<USER>/Documents/Python/agentlaboratory/data/Si2025_5_4.csv 加载数据。将 'Effective_Silicon' 列作为预测的目标变量，其余列作为特征。请执行完整的端到端数据科学流程：首先，进行探索性数据分析（EDA）；其次，对名义型字符特征使用独热编码，并进行特征工程和筛选，以识别影响有效硅产出的关键工艺参数；接着，构建并评估一个预测模型；最后，使用SHAP库进行模型解释和特征重要性可视化，并根据所有分析结果撰写一份详细的研究论文。" \
  --api-key "your_google_api_key_here" \
  --perplexity-api-key "your_perplexity_api_key_here" \
  --llm-backend "gemini-2.5-flash-preview-05-20" \
  --copilot-mode "True" \
  --language "中文" \
  --num-papers-lit-review "5" \
  --literature-engine "both" \
  --save-docx
```

这个升级版命令是 Agent Laboratory 的推荐运行方式，它充分利用了 Perplexity AI 的强大功能来增强文献综述阶段。`--perplexity-api-key` 的加入是关键，它使得平台能够访问 Perplexity AI 的多数据库搜索能力，从而获得比单一 ArXiv 引擎更全面、更实时的文献信息。`--literature-engine "both"` 参数确保了 Perplexity AI 和 ArXiv 的协同作用，最大化了文献覆盖范围。`--num-papers-lit-review "5"` 将检索的论文数量设定为 5 篇，这是一个平衡广度和深度的推荐值。其余参数与原始命令类似，保持了数据加载、特征工程、模型构建和解释的端到端流程。通过此命令，研究人员可以期待更深入的文献洞察，从而为后续的数据分析和模型开发提供更坚实的基础。

```bash
python ai_lab_repo.py \
  --research-topic "实验主题：粉煤灰制备硅肥有效硅机器学习。请从指定路径 /Users/<USER>/Documents/Python/agentlaboratory/data/Si2025_5_4.csv 加载数据。将 'Effective_Silicon' 列作为预测的目标变量，其余列作为特征。请执行完整的端到端数据科学流程：首先，进行探索性数 据分析（EDA）；其次，对名义型字符特征使用独热编码，并进行特征工程和筛选，以识别影响有效硅产出的关键工艺参数；接着，构建并评估一个预测模型；最后，使用SHAP库进行模型解释和特征重要性可视化，并根据所有分析结果撰写一份详细的研究论文。" \
  --api-key "your_openai_api_key_here" \
  --openai-base-url "https://hyapi-new.zmeng.xyz/v1" \
  --openai-model-name "[hy-F独享专线自营价格:0.095]gemini-2.5-pro-preview-06-05"\
  --perplexity-api-key "your_perplexity_api_key_here" \
  --llm-backend "openai-compatible" \
  --copilot-mode "True" \
  --language "中文" \
  --num-papers-lit-review "1" \
  --literature-engine "arxiv" \
  --save-docx \
  --load-existing "true" \
  --load-existing-path "save_saves/data_preparation.pkl"
```

此命令进一步展示了 Agent Laboratory 的灵活性，特别是在 LLM 后端配置方面。它将 `--llm-backend` 设置为 `"openai-compatible"`，并通过 `--openai-base-url` 和 `--openai-model-name` 指定了一个兼容 OpenAI API 的自定义模型。这对于那些拥有私有部署 LLM 或使用特定第三方 LLM 服务的用户来说非常有用，它允许用户利用其现有的 LLM 资源。`--num-papers-lit-review "1"` 将文献综述的论文数量限制为 1 篇，这适用于快速验证或在特定文献已知的场景。`--literature-engine "arxiv"` 再次将文献引擎切换回仅使用 ArXiv，这在 Perplexity API 密钥不可用或需要特定 ArXiv 检索时非常实用。`--load-existing "true"` 和 `--load-existing-path "save_saves/data_preparation.pkl"` 允许从之前的数据准备阶段继续研究，这对于迭代式研究流程至关重要。

**ArXiv 模式版本（立即可用，无需 Perplexity API 密钥）**：

```bash
python ai_lab_repo1.py \
  --research-topic "机器学习在电解铝电流效率预测的应用" \
  --api-key "your_openai_api_key_here" \
  --llm-backend "openai-compatible" \
  --copilot-mode "True" \
  --language "中文" \
  --num-papers-lit-review "2" \
  --literature-engine "arxiv" \
  --openai-base-url "https://openai.newbotai.cn/v1" \
  --openai-model-name "[满血伪流]gemini-2.5-pro"\
  --load-existing "True"\
  --load-existing-path "state_saves/data_preparation.pkl"\
  --save-docx
```

这个命令提供了一个无需 Perplexity API 密钥即可运行 Agent Laboratory 的选项，适用于那些暂时无法获取 Perplexity API 密钥或只需要基于 ArXiv 进行文献检索的用户。`--literature-engine "arxiv"` 明确指定了仅使用 ArXiv 作为文献来源。`--research-topic "机器学习在电解铝电流效率预测的应用"` 示例了另一个工业应用场景。此命令同样支持自定义 OpenAI 兼容的 LLM 后端，并允许从现有状态继续。它是一个灵活的替代方案，确保用户即使在特定 API 受限的情况下也能继续进行研究。

### 研究重点

**详细阐述：从数据加载到论文撰写，端到端数据科学流程的 Agent Laboratory 实现**

本研究项目的核心在于利用 Agent Laboratory 平台，针对粉煤灰制备硅肥有效硅的数据集，执行一个完整的端到端数据科学流程。这个流程不仅仅是简单的模型构建，更是一个系统性、迭代性的研究范式，旨在从原始数据中提取深层洞察，构建预测模型，并通过可解释性分析提升对工艺参数影响的理解，最终凝练成一篇高质量的研究论文。

**1. 数据加载与初始探索**

*   **数据来源与目标**: 研究首先从指定路径 `/Users/<USER>/Documents/Python/agentlaboratory/data/Si2025_5_4.csv` 加载数据集。这个数据集包含了与粉煤灰制备硅肥相关的各种工艺参数和结果指标。核心任务是将 `'Effective_Silicon'` 列作为预测的目标变量。这意味着模型将学习如何根据其他输入特征来预测有效硅的含量。
*   **特征选择与初步审查**: 除目标变量外，CSV 中的所有其他列都将被用作模型的特征。在数据加载后，Agent Laboratory 会自动进行初步的数据审查，包括检查数据类型、缺失值、异常值等，为后续的探索性数据分析 (EDA) 奠定基础。
*   **探索性数据分析 (EDA)**: EDA 是数据科学流程的第一步，旨在通过可视化和统计摘要来理解数据的内在结构、模式和关系。Agent Laboratory 将自动生成一系列 EDA 图表和报告，包括：
    *   **描述性统计**: 对所有数值特征进行均值、中位数、标准差、四分位数等统计分析，提供数据的基本概览。
    *   **目标变量分布**: 绘制 `'Effective_Silicon'` 的分布图（如直方图和 KDE 图），以了解其范围、偏度和峰度，这对于选择合适的模型和评估预测性能至关重要。
    *   **特征相关性分析**: 生成高级相关性矩阵可视化图。这个图表不仅显示特征之间的线性相关性（包括强正相关、强负相关和潜在的多重共线性），还会通过创新的可视化方式（如饼图元素）揭示更复杂的关系。这对于特征选择和理解变量间的相互作用至关重要。
    *   **参数交互图**: 绘制关键特征之间的交互图，以识别非线性关系和多变量效应。例如，通过散点图矩阵或特定特征对的联合分布图，观察不同工艺参数组合对有效硅产出的影响。这有助于发现潜在的协同或拮抗效应，为后续的特征工程提供依据。

**2. 特征工程与编码**

*   **名义型字符特征的独热编码**: 数据集中可能包含如 `'Phase'`、`'Additives'`、`'granular'` 和 `'water'` 等名义型（分类）字符特征。为了让机器学习模型能够理解这些非数值数据，Agent Laboratory 会自动对其进行独热编码 (One-Hot Encoding)。这意味着每个分类特征的每个唯一类别都将被转换为一个新的二进制（0 或 1）数值列。例如，如果 `'Phase'` 有“固态”和“液态”两个类别，它将被转换为 `Phase_固态` 和 `Phase_液态` 两列。这种编码方式避免了模型对分类特征的错误排序假设，并确保了模型能够公平地处理所有类别。
*   **高级特征工程**: 除了独热编码，Agent Laboratory 还会基于材料科学原理和领域知识，对现有特征进行深度工程，生成新的、更具信息量的派生特征。例如，可能会创建以下特征：
    *   **化学计量比**: 基于 SiO2、Al2O3、Na2CO3、Ca(OH)2 等组分的摩尔比，反映原料配比对反应的影响。例如，`Molar_Ratio_Na2CO3_SiO2`、`Molar_Ratio_CaOH2_SiO2`、`Molar_Ratio_CaOH2_Al2O3` 和 `Molar_Ratio_Alkali_Acid`。这些比值能够捕捉化学反应中的关键比例关系。
    *   **热力学与动力学特征**: 结合温度 (`Temp`) 和时间 (`Time`) 参数，生成反映反应过程热力学和动力学条件的特征，如 `Temp_Time_Interaction` (温度与时间的乘积)。
*   **特征筛选**: 在特征工程之后，可能会生成大量的特征。Agent Laboratory 会进行智能特征筛选，以识别对目标变量 `'Effective_Silicon'` 影响最大的关键工艺参数。这可以通过多种方法实现，例如基于相关性、模型重要性（如树模型的特征重要性）或递归特征消除 (RFE) 等。特征筛选有助于减少模型的复杂性，提高模型的泛化能力，并降低过拟合的风险。

**3. 模型构建与评估**

*   **多样化基学习器**: Agent Laboratory 采用系统性的方法构建和评估预测模型，不局限于单一模型。它会集成多种先进的机器学习回归器作为基学习器，包括但不限于：
    *   **K-近邻回归 (KNN)**: 一种非参数方法，根据最近邻的特征值进行预测。
    *   **随机森林 (RandomForest)**: 一种集成学习方法，通过构建多个决策树并取其平均预测来提高准确性和鲁棒性。
    *   **XGBoost**: 一种梯度提升树模型，以其高效性和卓越的性能在机器学习竞赛中广受欢迎。
    *   **LightGBM**: 另一个梯度提升框架，以其更快的训练速度和更低的内存消耗而闻名。
    *   **CatBoost**: 专为分类特征优化设计的梯度提升库，处理分类数据无需手动独热编码。
    *   **多层感知机 (MLP)**: 一种前馈神经网络，能够学习复杂的非线性关系。
    *   这些基学习器涵盖了不同的模型家族和学习机制，从而增加了找到最佳预测模型的可能性。
*   **Stacking 集成学习**: 为了进一步提升预测性能和模型的鲁棒性，Agent Laboratory 会构建一个 Stacking 集成模型。Stacking 是一种高级集成方法，它将多个基学习器的预测作为新的特征，然后训练一个元学习器（Meta-Learner，本例中是线性回归）来对这些预测进行组合。这种分层学习的方式能够有效利用每个基学习器的优势，并纠正它们的弱点，从而生成更准确、更稳定的最终预测。
*   **系统性调参与交叉验证**: 对于每个基学习器和 Stacking 模型，Agent Laboratory 都会执行系统性的超参数调优。这通常通过网格搜索 (GridSearchCV) 或随机搜索 (RandomizedSearchCV) 结合交叉验证 (Cross-Validation) 来实现。交叉验证确保了模型在未见过的数据上的泛化能力，避免了过拟合。
*   **模型评估指标**: 模型的性能将通过一系列标准的回归指标进行全面评估，包括：
    *   **R² 分数 (R-squared)**: 反映模型解释目标变量方差的比例，值越接近 1 越好。
    *   **均方误差 (Mean Squared Error, MSE)** 和 **均方根误差 (Root Mean Squared Error, RMSE)**: 衡量预测值与真实值之间的平均误差，值越小越好。
    *   **平均绝对误差 (Mean Absolute Error, MAE)**: 衡量预测值与真实值之间绝对误差的平均值，对异常值不敏感。
    *   平台会生成模型性能比较图和训练集与测试集 R² 对比图，直观展示不同模型的表现，并帮助识别过拟合或欠拟合问题。

**4. 模型解释与特征重要性可视化**

*   **SHAP (SHapley Additive exPlanations) 分析**: 模型的可解释性在科学研究和工业应用中至关重要。Agent Laboratory 将广泛使用 SHAP 库来解释模型的预测。SHAP 值基于合作博弈论，为每个特征在每个预测中的贡献分配一个值。通过 SHAP 分析，可以：
    *   **全局特征重要性**: 生成 SHAP 摘要图（条形图和点图），显示哪些特征对模型的整体预测影响最大。这有助于识别影响有效硅产出的最关键工艺参数。
    *   **局部解释**: 解释单个预测，理解特定样本中哪些特征导致了模型的特定预测结果。
    *   **特征依赖性**: 通过 SHAP 依赖图，揭示单个特征如何影响模型的预测，以及该影响是否与其他特征相互作用。
*   **PDP (Partial Dependence Plot) 分析**: 除了 SHAP，平台还会生成部分依赖图 (PDP)。PDP 显示了当一个或两个特征的值变化时，模型预测的平均变化趋势。这有助于可视化模型对特定特征的响应，并揭示特征与目标变量之间的边际关系。PDP 分析将包括：
    *   **综合 PDP 图**: 显示多个关键特征的独立影响。
    *   **个别特征的详细 PDP 图**: 提供高分辨率的图表，深入分析单个特征的影响。
    *   **基于重要性排序的 PDP 图**: 优先展示对模型预测影响最大的特征的 PDP。
*   **多层次 Stacking 模型解释**: 对于 Stacking 集成模型，解释过程将是多层次的：
    *   **第一层：基学习器解释**: 分别对每个基学习器进行 SHAP 分析，理解它们各自如何利用原始特征进行预测。
    *   **第二层：元学习器解释**: 对元学习器进行 SHAP 分析，揭示每个基学习器对最终 Stacking 模型预测的贡献权重。
    *   **第三层：整体模型黑盒解释**: 将整个 Stacking 模型视为一个黑盒，对其原始输入特征进行 SHAP 分析，以获得最终集成模型对原始特征的整体重要性视图。
    这些解释性分析将为研究人员提供深入的洞察，不仅知道“是什么”（预测结果），更知道“为什么”（驱动预测的因素），从而为工艺优化和决策制定提供科学依据。

**5. 撰写详细研究论文**

*   **结构化报告生成**: Agent Laboratory 的最终输出是撰写一份详细的研究论文。这不仅仅是简单的结果罗列，而是一篇结构完整、逻辑清晰、符合学术规范的报告。报告将包含以下关键章节：
    *   **摘要 (Abstract)**: 简洁概括研究背景、方法、主要发现和结论。
    *   **引言 (Introduction)**: 介绍研究背景、问题陈述、研究意义和本文结构。
    *   **文献综述 (Literature Review)**: 结合 Perplexity AI 的强大能力，对相关领域的现有研究进行全面、深入的综述，识别研究空白。
    *   **数据与特征工程 (Data and Feature Engineering)**: 详细描述数据集的来源、特征工程的方法和理由，以及关键特征的构建过程。
    *   **方法论 (Methodology)**: 阐述所采用的机器学习模型（包括基学习器和 Stacking 模型）、评估指标、超参数调优策略等。
    *   **结果与讨论 (Results and Discussion)**: 呈现模型评估结果、SHAP 和 PDP 分析结果，并深入讨论这些结果的科学含义、对工艺优化的指导意义，以及模型的优缺点。
    *   **结论 (Conclusion)**: 总结主要发现，并提出未来研究方向。
    *   **参考文献 (References)**: 自动生成符合学术标准的引用列表。
*   **专业级输出与定制**: 报告将采用专业级格式进行排版，包括图表、表格的插入和标注。用户可以通过 `--save-docx` 或 `--compile-latex` 参数选择输出格式。平台还将提供一定的定制选项，允许用户调整报告的特定部分，以满足特定期刊或会议的要求。
*   **迭代与完善**: 研究论文的撰写是一个迭代过程。Agent Laboratory 允许用户在生成初稿后，根据反馈进行修改和完善，从而逐步提高报告的质量。

## 🚀 Perplexity AI - 主要文献综述引擎

Agent Laboratory 的重大更新在于将 Perplexity AI 提升为**主要文献综述引擎**，这标志着我们在自动化研究领域迈出了重要一步。Perplexity AI 不仅仅是一个搜索引擎，它是一个能够理解复杂查询、整合多源信息、并生成连贯、准确回答的知识引擎。其与 Agent Laboratory 的深度融合，极大地提升了文献综述的效率、广度和深度。

### 🎯 核心优势的深度阐述

1.  **🥇 主要引擎**: Perplexity AI 现已成为 Agent Laboratory 在进行文献综述时的首选和默认工具。这意味着当代理需要获取背景知识、寻找相关研究或回答特定领域问题时，它将优先调用 Perplexity AI 的能力。这种优先级的设定是基于 Perplexity AI 在信息检索和内容整合方面的卓越表现，它能够提供比传统搜索引擎更聚焦、更精炼的学术信息。

2.  **🔍 多数据库覆盖**: Perplexity AI 能够自动搜索并整合来自多个权威学术数据库和信息源的内容，包括但不限于：
    *   **PubMed**: 专注于生物医学和生命科学领域的文献。
    *   **ArXiv**: 预印本服务器，涵盖物理、数学、计算机科学、生物学、经济学等领域最新研究。
    *   **Google Scholar**: 广泛的学术搜索引擎，索引了大量期刊、会议论文、学位论文和预印本。
    *   **IEEE Xplore**: 专注于电气工程、计算机科学和电子学领域的文献。
    *   **Semantic Scholar**: 一个基于 AI 的学术搜索和阅读工具，提供文献分析和推荐。
    这种多数据库覆盖确保了文献综述的全面性，避免了单一来源可能导致的信息偏颇或遗漏。Agent Laboratory 通过 Perplexity AI 能够从这些多样化的来源中提取最相关、最权威的信息，为您的研究提供坚实的基础。

3.  **🧠 AI 驱动分析**: Perplexity AI 不仅仅是检索信息，它还能进行智能内容理解、趋势识别和研究空白分析。它能够：
    *   **智能内容理解**: 深入分析文献文本，提取关键概念、方法论、实验结果和结论，并将其转化为可消化的摘要和要点。
    *   **趋势识别**: 通过分析大量文献，识别特定研究领域的热点、新兴趋势和发展方向，帮助研究人员把握前沿动态。
    *   **研究空白分析**: 基于现有文献的分析，智能识别当前研究中尚未解决的问题或未被充分探索的领域，为您的研究提供创新方向。
这种 AI 驱动的分析能力使得文献综述从简单的信息收集转变为深度的知识发现过程。

4.  **📝 专业级输出**: Agent Laboratory 结合 Perplexity AI 的能力，能够自动生成结构化、专业级的文献综述。输出的综述通常包含以下标准部分：
    *   **背景介绍**: 对研究主题的宏观背景进行概述。
    *   **研究现状**: 总结当前领域的主要进展和关键成果。
    *   **方法学**: 归纳不同文献中采用的研究方法和技术。
    *   **挑战与局限**: 指出当前研究面临的难题和不足。
    *   **未来展望**: 预测领域的发展方向和潜在突破。
    这种结构化的输出不仅提高了可读性，也确保了综述的全面性和专业性，可以直接用于学术报告或论文的撰写。

5.  **💰 成本控制**: 平台集成了智能预算管理和成本优化机制，以帮助用户有效地控制 Perplexity AI API 的使用成本。它会实时跟踪 API 调用量和消耗，并根据预设的预算限制自动调整文献检索策略，例如，在接近预算上限时，可能会优先检索高相关度的文献或限制检索深度，以确保在预算范围内获得最大化的价值。

6.  **🎨 智能模板**: Agent Laboratory 能够根据您指定的研究领域和主题，自动选择最优的文献综述模板和配置。这意味着您无需手动调整复杂的参数，系统会根据您的研究性质（例如，生物医学、计算机科学、材料科学等）自动优化文献检索策略、结果过滤和报告格式，从而提供定制化的、高效的文献综述体验。

### 🔑 API Key 配置的进一步说明

为了Agent Laboratory能够顺利调用Perplexity AI的服务，您需要正确配置Perplexity API Key。除了在命令行中直接指定参数外，我们更推荐通过环境变量进行设置，以提升安全性和便捷性。

#### 环境变量配置的详细操作

请参考前文“环境变量设置（推荐方式）”中关于 Linux/macOS 和 Windows 系统的详细步骤。确保将您的 Perplexity API Key（以 `pplx-` 开头）正确地赋值给 `PERPLEXITY_API_KEY` 环境变量。一旦设置完成，您将无需在每次运行命令时手动输入密钥，Agent Laboratory 会自动从环境中读取。

#### 密钥的获取与验证

*   **获取 Perplexity API 密钥**:
    1.  访问 Perplexity AI 的 API 设置页面：`https://www.perplexity.ai/settings/api`。
    2.  如果您还没有账户，请先注册并登录。
    3.  在 API 设置页面，您可以生成或查看您的 API 密钥。请注意，密钥通常以 `pplx-` 前缀开头。
    4.  务必妥善保管您的 API 密钥，不要随意分享或将其硬编码在公开的代码库中。
*   **验证密钥格式**: 确保您粘贴的密钥是完整的，并且符合 `pplx-` 开头的格式。任何字符的缺失或错误都可能导致认证失败。

### 📊 文献综述引擎选择的决策树

Agent Laboratory 提供了多种文献综述引擎选项，以适应不同的研究需求、预算限制和文献偏好。理解这些选项及其适用场景，将帮助您做出明智的选择。

| 选项 | 说明 | 推荐度 | 适用场景 | 考虑因素 |
|------|------|--------|----------|----------|
| `--literature-engine both` | **使用 Perplexity + ArXiv（推荐）**：此模式结合了 Perplexity AI 的广度和实时性（通过其多数据库搜索能力）以及 ArXiv 在预印本和新兴研究领域的深度。这是最全面的文献综述策略，能够捕获最新的研究进展和跨学科信息。 | ⭐⭐⭐⭐⭐ | 大多数研究场景，特别是需要全面、深入了解某个领域最新进展时；对文献覆盖范围和时效性有较高要求时。 | **成本**: 可能会产生较高的 Perplexity AI API 使用成本。 **性能**: 检索速度快，内容丰富。 |
| `--literature-engine perplexity` | **仅使用 Perplexity AI**：此模式完全依赖 Perplexity AI 进行文献检索和分析。它适用于您希望利用 Perplexity AI 强大的 AI 驱动分析能力，且对 ArXiv 以外的学术数据库有优先需求时。Perplexity AI 能够提供高度相关的摘要和综合性回答。 | ⭐⭐⭐⭐ | 优先考虑 AI 驱动的智能分析和多源整合能力；对实时信息和广度有较高要求，且愿意承担相应的 API 成本时。 | **成本**: 产生 Perplexity AI API 使用成本。 **性能**: 智能分析能力强，但可能不如 `both` 模式在特定预印本领域那么全面。 |
| `--literature-engine arxiv` | **仅使用 ArXiv 搜索**：此模式仅通过 ArXiv 数据库进行文献检索。ArXiv 是物理、数学、计算机科学等领域预印本的重要来源，适用于您主要关注这些领域且预算有限的情况。由于不使用 Perplexity AI，此模式通常不产生额外的 API 成本（除了 LLM 后端本身的成本）。 | ⭐⭐ | 预算有限；主要关注物理、数学、计算机科学等领域的最新预印本和已发表论文；对实时性要求不高，或已有明确的 ArXiv 论文目标时。 | **成本**: 通常较低（不产生 Perplexity AI API 成本）。 **性能**: 文献覆盖范围相对较窄，缺乏 AI 驱动的智能分析。 |

**决策建议**:
*   **默认推荐**: 对于大多数用户和研究项目，强烈建议使用 `--literature-engine both`。它提供了最佳的平衡，既能获取最新的、广泛的文献，又能进行智能分析。
*   **成本敏感型用户**: 如果您对成本非常敏感，并且您的研究领域主要集中在 ArXiv 涵盖的范围，那么 `--literature-engine arxiv` 是一个经济实惠的选择。
*   **特定需求**: 如果您需要 Perplexity AI 的特定分析能力，例如其对多模态信息或特定类型内容的处理能力，即使成本较高，也可以考虑 `--literature-engine perplexity`。

### 🎯 新增的文献综述命令

在 Agent Laboratory 的文献综述阶段，PhD 学生代理现在会**优先推荐**并使用以下命令，这些命令旨在简化与 Perplexity AI 的交互，并提高文献检索和分析的效率。

1.  **主要推荐命令**:
    ```
    ```PERPLEXITY_REVIEW
    您的研究主题或具体问题
    ```
    ```
    这个命令是与 Perplexity AI 进行文献综述的核心接口。当 PhD 学生代理需要进行全面的文献检索时，它会构造并执行此命令。您只需在 `PERPLEXITY_REVIEW` 标签内提供您的研究主题或具体问题，Agent Laboratory 就会自动调用 Perplexity AI，执行多数据库搜索，并生成一份结构化的文献综述。这个命令的优势在于其高度自动化和智能性，它能够理解复杂的自然语言查询，并返回高质量的综合性回答，极大地减轻了研究人员的工作负担。

2.  **补充命令**:
    为了支持更精细的文献综述操作，Agent Laboratory 还提供了以下补充命令，这些命令可以在特定场景下辅助主要综述流程：
    *   `SUMMARY`: 当您需要快速获取某篇特定论文的摘要时，可以使用此命令。这对于快速筛选大量文献，判断其相关性非常有用。例如：`SUMMARY [论文ID或标题]`。
    *   `FULL_TEXT`: 如果您需要获取某篇论文的完整文本内容进行深入分析，可以使用此命令。这通常在确定论文高度相关后使用。例如：`FULL_TEXT [论文ID或链接]`。
    *   `ADD_PAPER`: 如果您手动发现了一篇相关的论文，希望将其添加到 Agent Laboratory 的文献综述数据库中进行管理和分析，可以使用此命令。这有助于整合外部信息源。例如：`ADD_PAPER [论文标题或元数据]`。

这些命令共同构成了一个强大而灵活的文献综述工具集，使研究人员能够以更高效、更智能的方式管理和利用学术信息。

## 🚀 功能特点

Agent Laboratory 不仅仅是一个命令执行器，它是一个集成了多项智能功能的研究自动化平台。以下是其主要功能特点的详细阐述：

### 🔬 综合文献分析

Agent Laboratory 能够进行多维度、深层次的文献分析，超越了传统文献综述的范畴。

*   **多维度分析**: 平台能够从多个角度对文献进行分析，包括：
    *   **方法论**: 识别和总结不同研究中采用的实验方法、数据处理技术、模型构建策略等。这有助于研究人员了解当前领域的技术趋势和常用工具。
    *   **最新发展**: 关注研究领域的最新突破、新兴技术和前沿理论，确保综述内容的时效性。
    *   **研究空白**: 基于对现有文献的分析，智能识别当前研究中尚未充分探索的领域、未解决的问题或矛盾之处，为研究人员提供新的研究方向和创新点。
    这种多维度分析为研究人员提供了全面的视角，帮助他们更好地定位自己的研究并发现创新机会。

*   **自动结构化**: 平台能够自动将文献分析结果组织成结构化的专业级文献综述报告。报告通常包含以下标准部分：
    *   **背景**: 对研究主题的宏观背景进行概述，为读者提供必要的上下文信息。
    *   **现状**: 总结当前领域的主要研究进展、关键成果和已解决的问题。
    *   **方法**: 归纳不同文献中采用的研究方法和技术，并进行比较分析。
    *   **趋势**: 识别并预测领域未来的发展方向和潜在趋势。
    *   **挑战**: 指出当前研究面临的难题、瓶颈和局限性。
    这种自动结构化功能极大地节省了研究人员组织和撰写文献综述的时间，并确保了报告的专业性和一致性。

*   **智能格式化**: 除了内容结构化，Agent Laboratory 还能对文献综述进行智能格式化，使其符合学术出版物的要求。这包括：
    *   **专业级文献综述模板**: 平台内置了多种专业级的文献综述模板，可根据研究领域自动选择最合适的模板。
    *   **自动图表和表格生成**: 如果文献分析中涉及数据或趋势，平台能够自动生成相关的图表和表格，并将其插入到报告中。
    *   **引用管理**: 自动管理文献引用，确保引用格式符合学术标准（如 APA, MLA, Chicago 等），并生成完整的参考文献列表。
    这些格式化功能确保了最终报告的高质量和专业性。

### 💡 智能优化

Agent Laboratory 不仅自动化了研究流程，还通过智能优化策略，提升了效率和成本效益。

*   **自动配置**: 平台能够根据用户指定的研究主题和目标，自动选择并配置最优的模型和搜索域。这意味着，无需用户手动调整复杂的 LLM 模型参数或文献数据库选择，系统会智能地匹配最佳配置，以实现最佳的研究效果和成本效益。例如，对于需要深度分析的复杂主题，可能会自动选择更强大的 LLM 模型；对于需要实时信息的任务，可能会优先选择支持实时检索的文献引擎。
*   **成本控制**: Agent Laboratory 集成了先进的成本控制机制，旨在帮助用户有效管理 API 使用成本。
    *   **智能预算管理**: 用户可以设定一个总体的预算限制，平台会实时监控 API 调用量和消耗。当接近预算上限时，系统会发出警告，并可能自动调整策略，例如减少检索的文献数量或降低 LLM 的使用频率，以避免超出预算。
    *   **成本估算**: 在执行需要消耗 API 的任务之前，平台会提供预估的成本，帮助用户在开始前了解潜在的支出。
*   **策略推荐**: 根据当前的研究进展、可用资源和预算，Agent Laboratory 会智能推荐最优的研究策略。例如，如果文献综述阶段耗时过长或成本过高，系统可能会建议调整 `--num-papers-lit-review` 参数，或者推荐切换到更经济的文献引擎。这些策略推荐旨在帮助用户在效率、质量和成本之间找到最佳平衡点。

### 🌐 多语言支持

Agent Laboratory 提供了强大的多语言支持，特别是针对中文研究进行了优化，使其能够服务于全球范围内的研究人员。

*   **中文优化**: 平台在设计时充分考虑了中文语言的特点和学术表达习惯，对中文文献的检索、理解、分析和报告生成进行了专门优化。这包括对中文分词、语义理解、术语识别和报告撰写风格的精细调整，确保中文输出的流畅性、准确性和专业性。
*   **实时更新**: Agent Laboratory 能够获取并处理来自全球范围内的最新研究进展和发现，无论这些文献是以何种语言发表。通过与 Perplexity AI 等多语言信息源的集成，平台确保用户能够实时了解其研究领域的全球前沿动态。
*   **自动引用**: 平台能够自动生成符合学术标准的引用格式，支持多种国际和国内的引用风格。无论文献原文是何种语言，系统都能准确提取引用信息并进行格式化，大大减轻了研究人员在参考文献管理上的负担，确保了报告的学术严谨性。

## 💰 成本估算与控制

Agent Laboratory 致力于为用户提供透明且可控的成本管理体验。由于平台会调用外部 LLM 和文献检索 API，理解并管理这些成本至关重要。

### 模型成本细分

以下是 Perplexity AI 不同模型的成本估算（近似值），这些模型在性能和价格之间提供了不同的权衡：

| 模型 | 输入成本 (每1M Tokens) | 输出成本 (每1M Tokens) | 推荐用途 |
|------|----------|----------|----------|
| Sonar Pro | $1.00 | $1.00 | 适用于需要高度准确性和综合性分析的场景，如深度文献综述、复杂问题解决和详细报告生成。提供高质量的输出，但成本相对较高。 |
| Sonar Small | $0.20 | $0.20 | 适用于快速查询、初步文献筛选和需要快速响应的场景。成本效益高，适合进行大量低复杂度的信息检索。 |
| Sonar Huge | $5.00 | $5.00 | 专为需要极端深度分析和处理超大规模数据集的复杂研究任务设计。提供最高水平的智能和处理能力，但成本也最高。适用于需要突破性发现或高度定制化解决方案的尖端研究。 |

**注意**: 以上成本为近似值，实际成本可能因 API 提供商的定价策略、网络延迟和具体使用情况而异。Tokens 是衡量输入和输出文本长度的单位，通常一个词约等于 1.3-1.5 个 Tokens（对于英文）。

### 成本管理机制

Agent Laboratory 实施了多项策略，帮助用户有效管理和优化 API 使用成本：

1.  **自动预算管理**:
    *   **默认预算**: 平台默认设置了 `$15` 的预算限制。这意味着在没有用户明确指定的情况下，API 调用总成本不会超过此金额。
    *   **用户可配置**: 用户可以通过命令行参数或配置文件自定义预算限制。这为用户提供了灵活性，以适应不同的项目预算需求。
    *   **智能停止**: 当 API 使用成本接近或达到预设预算限制时，Agent Laboratory 会发出警告，并可以自动暂停或调整后续的 API 调用策略，以避免超出预算。

2.  **实时成本跟踪**:
    *   **透明化**: 平台会实时显示当前研究会话的 API 使用成本。这些成本通常会在终端输出中以近似值显示，例如 `Current experiment cost = $X.XX`。
    *   **决策支持**: 实时成本跟踪使用户能够随时了解其支出情况，从而根据预算和研究进展，及时调整研究策略或参数配置。

3.  **智能优化**:
    *   **策略推荐**: 基于实时成本和研究进展，Agent Laboratory 会智能推荐成本效益最高的策略。例如，如果当前模型成本过高，系统可能会建议切换到更经济的 LLM 模型（如 Sonar Small），或者减少文献综述的论文数量 (`--num-papers-lit-review`)。
    *   **资源动态分配**: 平台会尝试根据任务的复杂度和重要性，动态分配计算资源和 API 调用。例如，对于关键的文献综述阶段，可能会优先使用高性能模型；而在数据清洗等非核心阶段，则可能选择成本更低的资源。

通过这些成本估算和控制机制，Agent Laboratory 旨在为研究人员提供一个高效、透明且可控的自动化研究环境。

## ⚡ 快速启动指南

本节提供了一个简洁的快速启动指南，旨在帮助新用户迅速开始使用 Agent Laboratory，并运行他们的第一个研究项目。

### 1. 设置环境变量

在开始之前，确保您的系统已配置好必要的 API 密钥作为环境变量。这是最安全和最便捷的推荐方式。

```bash
export GOOGLE_API_KEY="your_google_api_key_here"
export PERPLEXITY_API_KEY="your_perplexity_api_key_here"
```

**详细步骤**: 请参考前文“🔑 环境变量设置（推荐方式）”一节，了解如何在不同操作系统（Linux/macOS, Windows）上设置环境变量，以及如何验证它们是否设置成功。确保您的 Perplexity API 密钥以 `pplx-` 开头。

### 2. 运行测试

在启动任何正式的研究项目之前，强烈建议您运行 Agent Laboratory 提供的测试脚本。这有助于验证您的环境配置是否正确，以及与外部服务的集成是否正常工作。

```bash
python test_perplexity.py
```

此命令将测试与 Perplexity AI 的核心集成。如果测试通过，则表示您的 API 密钥和网络连接正常。

### 3. 启动研究项目

一旦您的环境准备就绪并通过了测试，您就可以启动您的第一个研究项目了。

```bash
python ai_lab_repo.py \
  --research-topic "您的研究主题" \
  --literature-engine "both" \
  --language "中文" \
  --num-papers-lit-review "5"
```

**参数解释**:
*   `--research-topic`: 将 `"您的研究主题"` 替换为您实际的研究问题，例如 `"人工智能在医疗诊断中的应用"`。请尽量具体和明确，这将帮助代理更精准地聚焦研究。
*   `--literature-engine "both"`: 推荐使用“both”模式，同时利用 Perplexity AI 和 ArXiv 进行文献综述，以获取最全面的结果。
*   `--language "中文"`: 根据您的需求选择操作语言，例如“中文”或“English”。
*   `--num-papers-lit-review "5"`: 设定文献综述阶段检索的论文数量，可以根据研究的广度需求进行调整。

### 4. 查看结果

Agent Laboratory 在研究项目完成后，会在指定目录下生成详细的研究报告和相关文件。

*   **研究报告**: 最终的研究报告通常会保存在 `./research_dir/` 目录下，文件名为 `research_report.md` 或 `research_report.docx`（取决于 `--save-docx` 参数的设置）。这份报告将包含文献综述、数据分析、模型评估和解释等所有研究阶段的详细内容。
*   **对话记录**: 代理与用户之间的交互对话记录会保存在 `./research_dir/dialogue_*.json` 文件中。这些文件对于回顾研究过程、调试代理行为或理解决策路径非常有用。
*   **代码文件**: 在研究过程中生成的或修改的代码文件（例如数据处理脚本、模型训练脚本）可能会保存在 `./research_dir/src/` 目录下。这些文件反映了代理在研究中执行的具体操作和实现细节。

通过遵循这些快速启动步骤，您可以有效地利用 Agent Laboratory 自动化您的研究工作，并专注于更高层次的科学发现。

## 💡 使用建议

为了最大化 Agent Laboratory 的效用并确保研究项目的顺利进行，以下是一些重要的使用建议和最佳实践：

1.  **首次使用**:
    *   **建议使用交互模式 (`--copilot-mode "true"`)**: 对于初次接触 Agent Laboratory 的用户，强烈建议启用交互模式。在这种模式下，平台会在每个关键决策点暂停，并向您提供上下文信息和操作选项。这不仅能帮助您熟悉平台的工作流程和代理的决策逻辑，还能让您在早期阶段及时纠正潜在的方向性错误，从而避免不必要的资源浪费。通过逐步引导，您可以更好地理解每个参数的作用以及代理在不同阶段的思考过程。
    *   **从简单主题开始**: 建议选择一个相对简单、明确的研究主题作为您的第一个项目。这有助于您快速掌握平台的基本操作，并建立信心。

2.  **研究主题**:
    *   **使用具体、明确的研究问题**: 您的 `--research-topic` 应该尽可能具体和明确。模糊或宽泛的主题可能会导致代理检索到大量不相关的文献，或在数据分析时难以聚焦。例如，与其使用“人工智能”，不如使用“深度学习在药物发现中的最新进展”；与其使用“材料科学”，不如使用“粉煤灰制备硅肥有效硅的机器学习优化”。具体的研究问题能够更有效地指导代理的文献检索、数据处理和模型构建。
    *   **关键词优化**: 考虑在研究主题中包含核心关键词，以提高文献检索的准确性。

3.  **文献数量**:
    *   **根据研究复杂度调整 `--num-papers-lit-review`**: 这个参数直接影响文献综述的广度和深度。
        *   **快速概览**: 如果您需要对一个新领域进行初步了解，或者时间有限，可以将此参数设置为较小的值（例如 3-5 篇）。
        *   **深度研究**: 如果您需要撰写一篇全面的文献综述或博士论文，则可以将其设置为较大的值（例如 8-15 篇），但这会增加运行时间和 API 成本。
    *   **平衡广度与深度**: 在设定文献数量时，请权衡您对文献广度（覆盖面）和深度（详细分析）的需求。

4.  **成本控制**:
    *   **Perplexity API 有使用成本，建议从少量文献开始**: Perplexity AI 提供了强大的功能，但也伴随相应的 API 使用成本。在您熟悉平台和 Perplexity AI 的收费模式之前，建议从较小的 `--num-papers-lit-review` 值开始，并密切关注实时成本跟踪。
    *   **利用成本估算功能**: 在运行复杂任务前，尝试利用平台提供的成本估算功能，预估潜在的支出。
    *   **考虑使用 ArXiv 模式**: 如果预算非常有限，或者您的研究主要集中在 ArXiv 涵盖的领域，可以暂时切换到 `--literature-engine arxiv` 模式，以降低 API 成本。

5.  **数据准备**:
    *   **数据质量**: 确保您的输入数据质量高，没有严重的缺失值或异常值。高质量的数据是构建高性能模型的基础。
    *   **特征理解**: 在开始研究前，对数据中的特征有基本的理解，这将有助于您更好地评估代理的分析结果和模型解释。

6.  **迭代与优化**:
    *   **分阶段进行**: 对于大型或复杂的项目，建议分阶段进行研究，例如先完成数据准备和 EDA，然后进行模型构建，最后撰写报告。
    *   **利用 `--load-existing`**: 如果研究过程被中断或需要进行多次迭代，请充分利用 `--load-existing` 和 `--load-existing-path` 参数来恢复之前的状态，避免重复计算。

遵循这些建议将帮助您更有效地利用 Agent Laboratory，加速您的研究进程，并取得高质量的成果。

## 🚨 故障排除

在使用 Agent Laboratory 过程中，您可能会遇到一些问题。本节提供了常见问题的故障排除指南，特别是针对 Perplexity AI 集成相关的错误。

### Perplexity API 401 错误解决方案

当您遇到 "401 Unauthorized" 错误时，这通常意味着 Agent Laboratory 无法